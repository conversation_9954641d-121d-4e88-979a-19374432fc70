import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/admin_api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/admin_api/, '')
      }
    }
  },
  resolve: {
    alias: {
      // 配置@指向src目录
      '@': path.resolve(__dirname, './src')
    }
  }
})