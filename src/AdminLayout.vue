<template>
  <div class="admin-container">
    <!-- 登录组件 -->
    <LoginDialog 
      v-if="!isLoggedIn" 
      :visible="!isLoggedIn"
      @login-success="handleLoginSuccess"
    />
    
    <!-- 主布局组件 -->
    <MainLayout 
      v-if="isLoggedIn" 
      :current-user="currentUser"
      @logout="handleLogout"
    />
  </div>
</template>

<script setup lang="js">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import LoginDialog from './LoginDialog.vue';
import MainLayout from './MainLayout.vue';

// 状态管理
const isLoggedIn = ref(false);
const currentUser = ref({});
const router = useRouter();


// 处理登录成功
const handleLoginSuccess = (userData) => {
  currentUser.value = userData;
  isLoggedIn.value = true;
  localStorage.setItem('currentUser', JSON.stringify(userData));
  router.push('/home');
};

// 处理登出
const handleLogout = () => {
  currentUser.value = {};
  isLoggedIn.value = false;
  localStorage.removeItem('currentUser');
  router.push('/');
};

// 初始化
onMounted(() => {
  // 检查本地存储中是否有登录状态
  const savedUser = localStorage.getItem('currentUser');
  if (savedUser) {
    currentUser.value = JSON.parse(savedUser);
    isLoggedIn.value = true;
  }
});
</script>

<style scoped>
.admin-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
