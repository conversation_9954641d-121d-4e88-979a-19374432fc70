<template>
  <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :show-close="false"
      :width="loginDialogWidth"
      class="login-dialog"
      :before-enter="handleBeforeEnter"
  >
    <!-- 登录卡片容器 -->
    <div class="login-card">
      <!-- 品牌/系统图标 -->
      <div class="login-logo">
        <el-icon class="logo-icon"><Lock /></el-icon>
        <h3 class="logo-text">{{ appTitle }}</h3>
      </div>

      <!-- 登录表单 -->
      <el-form
          :model="loginForm"
          :rules="loginRules"
          ref="loginFormRef"
          label-width="80px"
          class="login-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              class="form-input"
              prefix-icon="User"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              class="form-input"
              prefix-icon="Lock"
          ></el-input>
        </el-form-item>

        <el-form-item class="submit-btn-container">
          <el-button
              type="primary"
              @click="handleLogin"
              class="login-btn"
              :loading="isLoading"
          >
            <template v-if="!isLoading">登录</template>
            <template v-if="isLoading">
              <Loading />
              <span>登录中...</span>
            </template>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 底部信息 -->
      <div class="login-footer">
        <span class="copyright">© 2025 在线咨询 版权所有</span>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="js">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Lock, Loading } from '@element-plus/icons-vue';

// 从环境变量获取应用标题
const appTitle = import.meta.env.VITE_APP_NAME || '管理系统';
const loginDialogWidth = '800px'; // 优化后的宽度比例

// 定义Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// 定义Emits
const emit = defineEmits(['login-success']);

// 状态管理
const dialogVisible = ref(props.visible);
const loginFormRef = ref(null);
const isLoading = ref(false); // 登录加载状态
const rememberMe = ref(false); // 记住我选项

const loginForm = ref({
  username: '',
  password: ''
});

const loginRules = ref({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
});

// 对话框进入前的动画准备
const handleBeforeEnter = (el) => {
  el.style.opacity = 0;
  el.style.transform = 'translateY(-20px)';
};

// 登录处理
const handleLogin = () => {
  // 模拟表单验证
  if (!loginForm.value.username || !loginForm.value.password) {
    ElMessage.error('请输入用户名和密码');
    return;
  }

  // 显示加载状态
  isLoading.value = true;

  // 模拟登录请求
  setTimeout(() => {
    // 根据用户名判断用户角色
    let userRole = 'viewer';
    if (loginForm.value.username === 'admin') {
      userRole = 'admin';
    } else if (loginForm.value.username === 'editor') {
      userRole = 'editor';
    }

    const userData = {
      username: loginForm.value.username,
      roles: [userRole],
      rememberMe: rememberMe.value,
      tenantId: '27754494105b11f081090242ac120002'
    };

    // 触发登录成功事件
    emit('login-success', userData);
    ElMessage.success('登录成功');

    // 重置表单和状态
    loginForm.value = {
      username: '',
      password: ''
    };
    isLoading.value = false;
  }, 800);
};
</script>

<style scoped>
  /* 对话框外层容器：关键调整宽高比例，让整体更协调 */
.login-dialog {
  --el-dialog-width: 800px; /* 改用 Element Plus 变量控制宽度，优先级更高 */
  --el-dialog-bg-color: #fafafa;
  /* 让对话框居中时垂直方向也更平衡，可根据需求微调 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 登录卡片容器：控制整体尺寸和内边距，让内容布局更舒展 */
.login-card {
  width: 100%;
  padding: 32px 24px;
  background: #fff;
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 品牌标识区域 */
.login-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px; /* 缩小底部间距，避免标题离表单太远 */
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.logo-icon {
  font-size: 48px;
  color: #165DFF;
  margin-bottom: 12px; /* 缩小图标与文字间距 */
  transition: transform 0.3s ease;
}

.logo-icon:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: 18px; /* 适度缩小标题，让比例更协调 */
  font-weight: 600;
  color: #1D2129;
  margin: 0;
}

/* 表单样式 */
.login-form {
  margin-top: 16px; /* 调整表单与标题间距 */
}

.form-input {
  --el-input-border-radius: 6px;
  height: 40px; /* 适度降低输入框高度，让整体更紧凑 */
  transition: all 0.2s;
  font-size: 14px;
}

.form-input:focus-within {
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.15);
}

/* 登录按钮样式 */
.submit-btn-container {
  margin-top: 16px; /* 调整按钮与操作区间距 */
}

.login-btn {
  width: 100%;
  height: 42px; /* 优化按钮高度，匹配输入框 */
  font-size: 14px;
  border-radius: 6px;
  background-color: #165DFF;
  border-color: #165DFF;
  transition: all 0.3s;
}

.login-btn:hover {
  background-color: #0E42D2;
  border-color: #0E42D2;
}

.login-btn:active {
  background-color: #0A34A3;
  border-color: #0A34A3;
}

/* 底部信息 */
.login-footer {
  margin-top: 20px; /* 缩小底部间距 */
  text-align: center;
}

.copyright {
  font-size: 12px;
  color: #86909C;
}
</style>