import { createRouter, createWebHistory } from 'vue-router'
import AdminLayout from './AdminLayout.vue'
import { ElMessage } from 'element-plus'

// 导入页面组件
const Home = () => import('./views/Home.vue')
const User = () => import('./views/User.vue')
const Setting = () => import('./views/Setting.vue')
const NotFound = () => import('./views/NotFound.vue')
const IntentCategoryManagement = () => import('./views/intent/IntentCategoryManagement.vue')
const Chat = () => import('./views/chat/Chat.vue')

const routes = [
  {
    path: '/',
    component: AdminLayout,
    children: [
      {
        path: '/home',
        name: 'Home',
        component: Home,
        meta: { title: '首页', roles: ['admin', 'editor', 'viewer'] }
      },
      {
        path: '/user',
        name: 'User',
        component: User,
        meta: { title: '用户管理', roles: ['admin', 'editor'] }
      },
      {
        path: '/setting',
        name: 'Setting',
        component: Setting,
        meta: { title: '系统设置', roles: ['admin'] }
      },
      {
        path: '/intentManagement',
        // 移除父路由的name，因为它没有component
        meta: { title: '意图管理', roles: ['admin'] },
        children: [
          {
            path: 'data',
            name: 'IntentCategory',
            component: IntentCategoryManagement,
            meta: { title: '意图数据管理', roles: ['admin'] }
          },
          {
            path: '', // 空路径子路由（默认路由）
            name: 'IntentManagement', // 将名字移到这里
            redirect: 'data'
          }
        ]
      },
      {
        path: '/chat',
        name: 'Chat',
        component: Chat,
        meta: { title: '聊天测试', roles: ['admin'] }
      },
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '页面不存在' }
  }
]


const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 权限控制
router.beforeEach((to, from, next) => {
  const currentUser = localStorage.getItem('currentUser') 
    ? JSON.parse(localStorage.getItem('currentUser')) 
    : null;
  
  // 未登录用户
  if (!currentUser) {
    // 允许访问登录相关页面
    return next();
  }
  
  // 检查权限
  if (to.meta.roles) {
    const hasPermission = to.meta.roles.some(role => 
      currentUser.roles.includes(role)
    );
    
    if (!hasPermission) {
      ElMessage.warning('没有访问权限');
      return next(from.path || '/home');
    }
  }
  
  next();
})

export default router