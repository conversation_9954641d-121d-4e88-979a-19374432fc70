<template>
  <div class="home-container">
    <h2>欢迎来到管理后台</h2>
    <p>当前用户: {{ currentUser.username }}</p>
    <p>用户角色: {{ currentUser?.roles?.[0] }}</p>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const currentUser = ref({});

onMounted(() => {
  const savedUser = localStorage.getItem('currentUser');
  if (savedUser) {
    currentUser.value = JSON.parse(savedUser);
  }
});
</script>

<style scoped>
.home-container {
  padding: 20px;
  text-align: center;
}
</style>
