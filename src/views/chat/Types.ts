import type {BlockReference, GenerationMode, IntentRecommend} from "@/api/qr_test.ts";

export enum Status {
    NEW,
    PROCESSING,
    DONE,
    ERROR,
    RISK,
    INTERRUPT
}

export enum SubStatus {
    NEW,
    PROCESSING,
    DONE,
}


export enum InterruptStatus {
    NOT,
    NETWORK,
    USER,
    ERROR,
}

export interface CurrentState {
    status: Status;
    intent_status: SubStatus;
    qa_status: SubStatus;
    interrupt_status: InterruptStatus;
}

export interface RefPair {
    mode: GenerationMode | null,
    ref: BlockReference | null
}

export type Reference = RefPair | null
export type Intent = IntentRecommend | null
