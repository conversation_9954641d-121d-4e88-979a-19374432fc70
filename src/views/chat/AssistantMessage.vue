<script setup lang="ts">
import { toRefs, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { DocumentCopy } from '@element-plus/icons-vue';
import { Status, SubStatus, type CurrentState, type Intent, type Reference } from "@/views/chat/Types.ts";
import { GenerationMode, IntentStatus } from "@/api/qr_test.ts";

interface Props {
  content: string,
  state: CurrentState,
  reference: Reference,
  intent: Intent
}

const props = defineProps<Props>()
const { content, state, reference, intent } = toRefs(props)

// 计算显示的内容
const displayContent = computed(() => {
  if (state.value.status === Status.RISK) {
    return "检测到您的问题存在不合规信息！"
  }
  return content.value
})

// 判断是否为终止状态
const isTerminalStatus = computed(() => {
  return [Status.DONE, Status.INTERRUPT, Status.ERROR, Status.RISK].includes(state.value.status)
})

// 判断是否显示错误提示区域
const showErrorArea = computed(() => {
  return isTerminalStatus.value && (state.value.status === Status.INTERRUPT || state.value.status === Status.ERROR)
})

// 错误提示文案
const errorMessage = computed(() => {
  if (state.value.status === Status.INTERRUPT) {
    return "当前回复被打断，已停止输出"
  }
  if (state.value.status === Status.ERROR) {
    return "回复过程中发生错误！"
  }
  return ""
})

// 判断是否显示知识引用区域
const showReferenceArea = computed(() => {
  return state.value.status === Status.DONE &&
         state.value.qa_status === SubStatus.DONE &&
         reference.value !== null
})

// 生成方式文案
const generationModeText = computed(() => {
  if (!reference.value?.mode) return ""

  if (reference.value.mode === GenerationMode.BY_LLM || reference.value.mode === GenerationMode.DEFAULT) {
    return "回复内容由AI生成，未参考任何知识，请仔细甄别！"
  }
  if (reference.value.mode === GenerationMode.REF_KB) {
    return "回复内容引用了下述知识/文档，请参考"
  }
  return ""
})

// 生成方式样式类
const generationModeClass = computed(() => {
  if (!reference.value?.mode) return ""

  if (reference.value.mode === GenerationMode.BY_LLM || reference.value.mode === GenerationMode.DEFAULT) {
    return "warning-text"
  }
  if (reference.value.mode === GenerationMode.REF_KB) {
    return "info-text"
  }
  return ""
})

// 文档引用列表
const documentList = computed(() => {
  return reference.value?.ref?.document_reference_list || []
})

// 判断是否显示意图识别区域
const showIntentArea = computed(() => {
  return state.value.status === Status.DONE &&
         state.value.intent_status === SubStatus.DONE &&
         intent.value !== null &&
         intent.value.recognized_status !== IntentStatus.NO_MATCH
})

// 意图推荐列表
const intentRecommendList = computed(() => {
  return intent.value?.recommend_list || []
})

// 复制消息内容
const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(content.value)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    console.error("复制失败", err)
    ElMessage.error('复制失败')
  }
}
</script>

<template>
  <div class="message assistant-message">
    <!-- 主要消息内容 -->
    <div class="bubble markdown-body" v-html="displayContent"></div>

    <!-- 额外信息区域 - 只在终止状态且不是RISK时显示 -->
    <div v-if="isTerminalStatus && state.status !== Status.RISK" class="extra-areas">
      <!-- 1. 错误提示区域 -->
      <div v-if="showErrorArea" class="error-area">
        <div class="error-message">{{ errorMessage }}</div>
      </div>

      <!-- 2. 知识引用信息区域 -->
      <div v-if="showReferenceArea" class="reference-area">
        <!-- 内容生成方式文案 -->
        <div class="generation-mode" :class="generationModeClass">
          {{ generationModeText }}
        </div>

        <!-- 知识引用列表 -->
        <div v-if="reference?.ref && documentList.length > 0" class="document-list">
          <div
            v-for="doc in documentList"
            :key="doc.document_id"
            class="document-item"
          >
            {{ doc.document_name }}
          </div>
        </div>
      </div>

      <!-- 3. 意图识别信息区域 -->
      <div v-if="showIntentArea" class="intent-area">
        <div class="intent-list">
          <div
            v-for="item in intentRecommendList"
            :key="item.intent_id"
            class="intent-item"
          >
            {{ item.content }}【{{ item.tool?.scene_name || '' }}】
          </div>
        </div>
      </div>

      <!-- 4. 操作栏区域 -->
      <div class="operation-area">
        <el-icon class="copy-icon" @click="copyMessage" title="复制消息">
          <DocumentCopy />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 代码块在深色背景下的可读性提升 */
:global(.markdown-body) pre code {
  font-size: 13px;
  line-height: 1.6;
}

/* 助手消息（左侧，Markdown 渲染） */
.assistant-message {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 8px;
  position: relative;
}

.assistant-message .bubble {
  max-width: 100%;
  /* 背景透明 */
  background: transparent;
  /* 保持不变 */
  padding: 18px 24px;
  border-radius: 12px;
  box-shadow: none;
  word-wrap: break-word;
  overflow-x: auto;
  /* 黑色字体 */
  color: #000000;
  display: block;
  text-align: left;
  /* assistant 消息字体更大，进一步放大 */
  font-size: 16px;
  /* 助手消息行间距 */
  line-height: 1.8;
}

/* 额外信息区域 */
.extra-areas {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: 100%;
  gap: 8px;
  margin-top: 8px;
}

/* 1. 错误提示区域 */
.error-area {
  align-self: flex-end;
}

.error-message {
  background: #fef0f0;
  color: #f56c6c;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #fbc4c4;
  font-size: 14px;
}

/* 2. 知识引用信息区域 */
.reference-area {
  align-self: flex-end;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 80%;
}

.generation-mode {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  text-align: right;
}

.warning-text {
  background: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.info-text {
  background: #ecf5ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.document-item {
  background: #f5f7fa;
  color: #606266;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  text-align: right;
  border: 1px solid #e4e7ed;
}

/* 3. 意图识别信息区域 */
.intent-area {
  align-self: flex-end;
  max-width: 80%;
}

.intent-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.intent-item {
  background: #f4f4f5;
  color: #606266;
  padding: 5px 15px;
  border-radius: 4px;
  font-size: 13px;
  text-align: right;
  border: 1px solid #dcdfe6;
  display: inline-block;
  align-self: flex-end;
}

/* 4. 操作栏区域 */
.operation-area {
  align-self: flex-end;
  visibility: hidden; /* 默认隐藏 */
}

/* 悬浮消息时显示操作栏 */
.assistant-message:hover .operation-area {
  visibility: visible;
}

.copy-icon {
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: color 0.2s;
}

.copy-icon:hover {
  color: #000;
}
</style>
