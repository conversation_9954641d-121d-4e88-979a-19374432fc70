<script setup lang="ts">
import { toRefs, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowRight, DocumentCopy } from '@element-plus/icons-vue';
import { Status, SubStatus, type CurrentState, type Intent, type Reference } from "@/views/chat/Types.ts";
import { GenerationMode, IntentStatus } from "@/api/qr_test.ts";

interface Props {
  content: string,
  state: CurrentState | null,
  reference: Reference | null,
  intent: Intent | null
}

const props = defineProps<Props>()
const { content, state, reference, intent } = toRefs(props)

// 计算显示的内容
const displayContent = computed(() => {
  if (state?.value?.status === Status.RISK) {
    return "检测到您的问题存在不合规信息！"
  }
  return content.value
})

// 判断是否为终止状态
const isTerminalStatus = computed(() => {
  if (!state.value) return false
  return [Status.DONE, Status.INTERRUPT, Status.ERROR, Status.RISK].includes(state.value.status)
})

// 判断是否显示错误提示区域
const showErrorArea = computed(() => {
  if (!state.value) return false
  return isTerminalStatus.value && (state.value.status === Status.INTERRUPT || state.value.status === Status.ERROR)
})

// 错误提示文案
const errorMessage = computed(() => {
  if (!state.value) return ""
  if (state.value.status === Status.INTERRUPT) {
    return "当前回复被打断，已停止输出"
  }
  if (state.value.status === Status.ERROR) {
    return "回复过程中发生错误！"
  }
  return ""
})

// 判断是否显示知识引用区域
const showReferenceArea = computed(() => {
  if (!state.value) return false
  return state.value.status === Status.DONE &&
         state.value.qa_status === SubStatus.DONE &&
         reference.value !== null
})

// 生成方式文案
const refCount = computed(() => {
  const count = reference?.value?.ref?.document_reference_list?.length || 0
  return count.toString()
})

const isRefKb = computed(() => {
  if (!reference.value?.mode) return false

  if (reference.value.mode === GenerationMode.BY_LLM || reference.value.mode === GenerationMode.DEFAULT) {
    return false
  }
  return reference.value.mode === GenerationMode.REF_KB;

})

// 文档引用列表
const documentList = computed(() => {
  return reference.value?.ref?.document_reference_list || []
})

// 判断是否显示意图识别区域
const showIntentArea = computed(() => {
  if (!state.value) return false
  return state.value.status === Status.DONE &&
         state.value.intent_status === SubStatus.DONE &&
         intent.value !== null &&
         intent.value.recognized_status !== IntentStatus.NO_MATCH
})

// 意图推荐列表
const intentRecommendList = computed(() => {
  return intent.value?.recommend_list || []
})

// 复制消息内容
const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(content.value)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    console.error("复制失败", err)
    ElMessage.error('复制失败')
  }
}
</script>

<template>
  <div class="message assistant-message">
    <!-- 主要消息内容 -->
    <div class="bubble markdown-body" v-html="displayContent"></div>

    <!-- 额外信息区域 -->
    <div class="extra-areas">

      <!-- 1. 引用类型说明 -->
      <template v-if="showReferenceArea">
        <div v-if="isRefKb" class="generation-mode generation-mode-ref-kb">
          <span style="text-decoration: underline">以上回复内容引用了 {{ refCount }} 篇知识文档，点击查看</span>
          <el-icon class="arrow-icon">
            <ArrowRight />
          </el-icon>
        </div>
        <div v-else class="generation-mode generation-mode-by-llm">
          <span>警告：以上回复内容由AI生成，未参考任何知识，请仔细甄别！</span>
        </div>
      </template>

      <!-- 2. 操作栏区域 - 始终保留空间但默认隐藏 -->
      <div class="operation-area" v-show="state?.status === Status.DONE">
        <el-icon class="copy-icon" @click="copyMessage" title="复制消息" style="font-size: 20px">
          <DocumentCopy />
        </el-icon>
      </div>


      <!-- 3. 错误提示区域 - 只在需要时显示，不保留空间 -->
      <div v-if="showErrorArea" class="error-area">
        <div class="error-message">{{ errorMessage }}</div>
      </div>

      <!-- 4. 知识引用信息区域 - 只在需要时显示，不保留空间 -->
<!--      <div v-if="showReferenceArea" class="reference-area">-->
<!--        &lt;!&ndash; 知识引用列表 &ndash;&gt;-->
<!--        <div v-if="reference?.ref && documentList.length > 0" class="document-list">-->
<!--          <div-->
<!--            v-for="doc in documentList"-->
<!--            :key="doc.document_id"-->
<!--            class="document-item"-->
<!--          >-->
<!--            {{ doc.document_name }}-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->

      <!-- 5. 意图识别信息区域 - 只在需要时显示，不保留空间 -->
      <div v-if="showIntentArea" class="intent-area">
        <el-divider />
        <div class="intent-list">
          <div
              v-for="item in intentRecommendList"
              :key="item.intent_id"
              class="intent-item"
          >
            <span class="intent-text">{{ item.content }}</span>
            <!-- Element Plus 的向右箭头图标 -->
            <el-icon class="arrow-icon">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<style scoped>
/* 代码块在深色背景下的可读性提升 */
:global(.markdown-body) pre code {
  font-size: 13px;
  line-height: 1.6;
}

/* 助手消息（左侧，Markdown 渲染） */
.assistant-message {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  margin-bottom: 5px;
  margin-top: 5px;
}

.assistant-message .bubble {
  max-width: 100%;
  /* 背景透明 */
  background: transparent;
  /* 保持不变 */
  padding: 5px 0;
  border-radius: 12px;
  box-shadow: none;
  word-wrap: break-word;
  overflow-x: auto;
  /* 黑色字体 */
  color: #000000;
  display: block;
  text-align: left;
  /* assistant 消息字体更大，进一步放大 */
  font-size: 16px;
  /* 助手消息行间距 */
  line-height: 1.8;
}

/* 额外信息区域 */
.extra-areas {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  gap: 8px;
}

/* 1. 错误提示区域 */
.error-area {
  align-self: flex-start;
}

.error-message {
  background: #fef0f0;
  color: #f56c6c;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #fbc4c4;
  font-size: 14px;
}

/* 2. 知识引用信息区域 */
.reference-area {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-self: stretch;
}

.generation-mode {
  padding-top: 2px;
  padding-bottom: 2px;
  font-size: 14px;
  text-align: left;
  /* 关键点 */
  align-self: flex-start;
  margin-bottom: 10px;
  font-weight: bold;
}

/* AI生成内容提示样式（黄色警告色调） */
.generation-mode-by-llm {
  align-self: stretch;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin-top: 10px;
  border-radius: 8px;
  background-color: #fffbf0; /* 浅黄背景 */
  border: 1px solid #ffeaa7; /* 黄色边框 */
  color: #92400e; /* 深棕文字 */
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.2s ease;
}

.generation-mode-by-llm::before {
  content: "⚠️"; /* 警告图标更符合提示含义 */
  font-size: 18px;
}

.generation-mode-by-llm:hover {
  background-color: #fff3c4; /* 略深的黄色背景 */
  border-color: #facc15; /* 亮黄色边框 */
}

/* 引用知识提示样式（保持不变） */
.generation-mode-ref-kb {
  align-self: stretch;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin-top: 10px;
  border-radius: 8px;
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.2s ease;
}

.generation-mode-ref-kb:hover .arrow-icon {
  transform: translateX(3px);  /* 箭头轻微右移，增强交互感 */
}

.generation-mode-ref-kb::before {
  content: "📚";
  font-size: 18px;
}

.generation-mode-ref-kb:hover {
  background-color: #dcfce7;
  border-color: #86efac;
}

.warning-text {
  background: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.info-text {
  background: #ecf5ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.document-item {
  background: #f5f7fa;
  color: #606266;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  text-align: left;
  border: 1px solid #e4e7ed;
}

/* 3. 意图识别信息区域 */
.intent-area {
  align-self: stretch;
  max-width: 100%;
}

.intent-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 12px;
  padding: 16px;
}

.intent-item {
  background: #f1f1f1;
  color: darkslateblue;
  font-weight: bold;
  min-width: 20%;
  padding: 10px 22px;
  border-radius: 20px;
  text-align: left;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

/* 文本部分样式 */
.intent-text {
  margin-right: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* 箭头图标样式 */
.arrow-icon {
  transition: transform 0.2s ease;
  font-size: 16px;
}

/* 悬停效果 */
.intent-item:hover {
  background: #e8e8e8;
}

.intent-item:hover .arrow-icon {
  transform: translateX(3px);  /* 箭头轻微右移，增强交互感 */
}

/* 4. 操作栏区域 */
.operation-area {
  align-self: flex-start;
}

/* 悬浮消息时显示操作栏 */
.assistant-message:hover .operation-area {
  visibility: visible;
}

.copy-icon {
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: color 0.2s;
}

.copy-icon:hover {
  color: #000;
}
</style>
