<script setup lang="ts">
import { toRefs } from 'vue';
import {type CurrentState, type Intent, type Reference} from "@/views/chat/Types.ts";

interface Props {
  content: string,
  state: CurrentState,
  reference: Reference,
  intent: Intent
}

const props = defineProps<Props>()
const { content, state, reference, intent } = toRefs(props)
</script>

<template>
  <div class="message assistant-message">
    <div class="bubble markdown-body" v-html="content"></div>
  </div>
</template>

<style scoped>
/* 代码块在深色背景下的可读性提升 */
:global(.markdown-body) pre code {
  font-size: 13px;
  line-height: 1.6;
}

/* 助手消息（左侧，Markdown 渲染） */
.assistant-message {
  display: flex;
  justify-content: flex-start;
}

.assistant-message .bubble {
  max-width: 100%;
  /* 背景透明 */
  background: transparent;
  /* 保持不变 */
  padding: 18px 24px;
  border-radius: 12px;
  box-shadow: none;
  word-wrap: break-word;
  overflow-x: auto;
  /* 黑色字体 */
  color: #000000;
  display: block;
  text-align: left;
}

.assistant-message .bubble {
  /* assistant 消息字体更大，进一步放大 */
  font-size: 16px;
  /* 助手消息行间距 */
  line-height: 1.8;
}
</style>
