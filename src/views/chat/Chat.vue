<script setup lang="ts">
import { ref, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { marked } from 'marked'
import { markedHighlight } from 'marked-highlight'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'
import 'github-markdown-css/github-markdown-dark.css'

import { sendChatTestSSE, type Block, type ChatTestRequest, Server, Role } from '@/api/qr_test'

// ============ 状态 ============
const inputMessage = ref('')
const messages = ref<{ role: 'user' | 'assistant'; content: string }[]>([])
// 存放每条消息的已渲染 HTML（仅助手消息使用）
const renderedContent = ref<string[]>([])
const isLoading = ref(false)
const chatContainer = ref<HTMLElement>()

// 打字机相关
// 待输出队列（把 SSE 收到的文本先塞进来）
const typingQueue = ref('')
// 定时器 id
const typingTimer = ref<number | null>(null)
// 当前正在打字的消息索引（助手消息）
const typingIndex = ref<number | null>(null)

// ============ marked 配置（v5 用插件） ============
marked.use(
    markedHighlight({
      langPrefix: 'hljs language-',
      highlight(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          return hljs.highlight(code, { language: lang }).value
        }
        return hljs.highlightAuto(code).value
      }
    })
)
marked.setOptions({
  breaks: true
})

// ============ 发送消息 ============
const sendMessage = async () => {
  if (!inputMessage.value.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }
  if (isLoading.value) {
    ElMessage.warning('正在发送中，请稍候...')
    return
  }

  const userMessage = inputMessage.value.trim()
  messages.value.push({ role: 'user', content: userMessage })
  inputMessage.value = ''
  isLoading.value = true
  await nextTick()
  scrollToBottom()

  try {
    const requestData: ChatTestRequest = {
      query: userMessage,
      mode: 'qa',
      server: Server.DEV,
      role: Role.USER
    }

    // 新增一条助手消息（流式渲染目标）
    const assistantIdx = messages.value.length
    messages.value.push({ role: 'assistant', content: '' })
    renderedContent.value[assistantIdx] = ''
    typingIndex.value = assistantIdx

    // 启动 SSE
    await sendChatTestSSE(requestData, {
      onData: (block: Block) => {
        if (block.message?.content) {
          // 把新内容塞进队列，打字机定时器会慢慢吐出
          typingQueue.value += block.message.content
          startTypewriter()
        }
        if (block.code !== 200 && block.error) {
          ElMessage.error(`服务器错误: ${block.error}`)
        }
      },
      onError: (error: Error) => {
        ElMessage.error(`连接错误: ${error.message}`)
        typingQueue.value += `\n[错误: ${error.message}]\n\n`
        startTypewriter()
      },
      onComplete: () => {
        isLoading.value = false
      }
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
    isLoading.value = false
  }
}

// ============ 打字机（逐字渲染） ============
const startTypewriter = () => {
  if (typingTimer.value !== null) return
  if (typingIndex.value === null) return

  // 每 tick 输出 2～4 个字符，既有“打字感”又更顺滑
  const CHUNK_MIN = 2
  const CHUNK_MAX = 4
  const INTERVAL = 12 // ms

  typingTimer.value = window.setInterval(() => {
    if (!typingQueue.value.length) {
      // 队列空了，关定时器，等待下一段
      if (typingTimer.value !== null) {
        clearInterval(typingTimer.value)
        typingTimer.value = null
      }
      return
    }

    const take = Math.min(
        typingQueue.value.length,
        Math.floor(Math.random() * (CHUNK_MAX - CHUNK_MIN + 1)) + CHUNK_MIN
    )
    const piece = typingQueue.value.slice(0, take)
    typingQueue.value = typingQueue.value.slice(take)

    const idx = typingIndex.value!
    messages.value[idx].content += piece
    renderedContent.value[idx] = marked.parse(messages.value[idx].content) as string


    nextTick(() => scrollToBottom())
  }, INTERVAL)
}

onBeforeUnmount(() => {
  if (typingTimer.value !== null) clearInterval(typingTimer.value)
})

// ============ 滚动到底部 ============
const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

// 处理回车发送
const handleEnter = () => {
  if (inputMessage.value.trim()) {
    sendMessage()
  }
}

// Shift+Enter 插入换行（其实默认就是换行，这里写不写都行）
// 如果想保留默认行为，可以不用写
const handleShiftEnter = () => {
}

</script>

<template>
  <div class="chat-container">
    <!-- 聊天内容 -->
    <div ref="chatContainer" class="chat-content">
      <div class="chat-messages">
        <template v-for="(msg, index) in messages" :key="index">
          <!-- 用户消息：右对齐灰色气泡 -->
          <div v-if="msg.role === 'user'" class="message user-message">
            <div class="bubble">{{ msg.content }}</div>
          </div>

          <!-- 助手消息：左对齐，Markdown + 高亮；流式时不断更新 renderedContent[index] -->
          <div v-else class="message assistant-message">
            <div class="bubble markdown-body" v-html="renderedContent[index]"></div>
          </div>
        </template>
      </div>
    </div>

    <!-- 底部输入区：居中 50%，按钮与输入框等高 -->
    <div class="chat-input">
      <div class="input-container">
        <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="1"
            :autosize="{ minRows: 1, maxRows: 8 }"
            placeholder="输入消息..."
            :disabled="isLoading"
            resize="none"
            @keyup.enter.exact.prevent="handleEnter"
            @keyup.shift.enter="handleShiftEnter"
        />
        <el-button
            type="primary"
            class="send-button"
            :loading="isLoading"
            :disabled="!inputMessage.trim()"
            @click="sendMessage"
        >
          {{ isLoading ? '发送中...' : '发送' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;   /* 交给父容器决定 */
  background: #343541;
  color: #000000; /* 默认字体黑色 */
}

/* 内容区：只它滚动 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  background: #f5f5f5;  /* 浅灰色，可换成 #ffffff 白色 */
  min-height: 0;  /* 避免 flex 子项撑爆父容器 */
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 50%;
  margin: 0 auto; /* 居中消息列 */
}

/* 消息字体大小调整 */
.user-message .bubble {
  font-size: 14px;   /* user 消息字体 */
  line-height: 1.2;  /* 用户消息行间距 */
}

.assistant-message .bubble {
  font-size: 18px;   /* assistant 消息字体更大，进一步放大 */
  line-height: 1.5;  /* 助手消息行间距 */
}

/* 用户消息（右侧浅灰气泡） */
.user-message {
  display: flex;
  justify-content: flex-end;
}

.user-message .bubble {
  max-width: 100%;
  background: #f0f0f0; /* 更浅的灰色背景 */
  color: #000000;       /* 黑色字体 */
  padding: 10px 16px;   /* 上下padding减小 */
  border-radius: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.25);
  white-space: pre-wrap;
}

/* 助手消息（左侧，Markdown 渲染） */
.assistant-message {
  display: flex;
  justify-content: flex-start;
}

.assistant-message .bubble {
  max-width: 100%;
  background: transparent;  /* 背景透明 */
  padding: 18px 24px;       /* 保持不变 */
  border-radius: 12px;
  box-shadow: none;
  word-wrap: break-word;
  overflow-x: auto;
  color: #000000;           /* 黑色字体 */
  display: block;
  text-align: left;
}

/* 代码块在深色背景下的可读性提升 */
:global(.markdown-body) pre code {
  font-size: 13px;
  line-height: 1.6;
}

/* 底部输入区：居中 50%，按钮与输入框等高 */
.chat-input {
  background: #ffffff;  /* 改为白色背景 */
  border-top: 1px solid #cccccc;
  padding: 18px 0;      /* 原 12px，1.5倍 */
  display: flex;
  justify-content: center;
}

.input-container {
  display: flex;
  width: 50%;
  gap: 8px;
  align-items: stretch; /* 子项等高 */
}

/* 输入框：白底黑字，灰色边框 */
:deep(.el-textarea__inner) {
  background: #ffffff;
  color: #000000;
  border: 1px solid #999999; /* 灰色边框 */
  caret-color: #000000;
  min-height: 66px;           /* 原 44px 放大1.5倍 */
}
:deep(.el-textarea__inner::placeholder) {
  color: #666666;
}

/* 发送按钮：深黑色，悬浮略浅，高度放大 */
.send-button {
  align-self: stretch;
  height: auto;               /* 自适应填满容器高度 */
  padding: 0 16px;
  background: #111111;        /* 深黑色 */
  border-color: #111111;
  color: #ffffff;
}
.send-button:hover,
.send-button:focus {
  background: #333333;        /* 悬浮略浅 */
  border-color: #333333;
}

/* 自定义滚动条 */
.chat-content::-webkit-scrollbar {
  width: 8px;
}
.chat-content::-webkit-scrollbar-thumb {
  background: #565869;
  border-radius: 4px;
}
</style>
