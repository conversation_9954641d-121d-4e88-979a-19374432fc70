<script setup lang="ts">
import { ref, nextTick, onBeforeUnmount, inject, computed, type Ref, watchEffect } from 'vue'
import { ElMessage } from 'element-plus'
import { marked } from 'marked'
import { markedHighlight } from 'marked-highlight'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark-dimmed.min.css'
import {
  sendChatTestSSE,
  type Block,
  type ChatTestRequest,
  Server,
  Role,
  type BlockReference,
  type IntentRecommend, BlockSource, GenerationMode,
} from '@/api/qr_test'
import AssistantMessage from "@/views/chat/AssistantMessage.vue";
import UserMessage from "@/views/chat/UserMessage.vue";
import type { AreaMap } from '@/api/area.ts'
import {
  type CurrentState,
  Status,
  InterruptStatus,
  SubStatus, type Reference, type Intent
} from '@/views/chat/Types'

// ============ 地域数据 ============
// 地域数据
const areaInfo = inject<Ref<AreaMap>>('areaInfo')
if (!areaInfo) throw new Error('areaInfo 未注入')

// 省份选项
const provinceOptions = computed(() => {
  if (!areaInfo.value?.province || typeof areaInfo.value.province !== 'object') {
    return [];
  }
  const provinceDict = areaInfo.value.province;
  return Object.values(provinceDict).map(item => ({
    province_code: item.province_code,
    area_name: item.area_name
  }));
});

// ============ 配置选项 ============
const selectedServer = ref<Server>(Server.TEST)
const selectedProvince = ref<string>('100000') // 先设置默认值
const selectedRole = ref<Role>(Role.USER)

// 监听省份选项变化，设置默认值为"总部"
watchEffect(() => {
  if (provinceOptions.value.length > 0 && selectedProvince.value === '100000') {
    const headquarters = provinceOptions.value.find(item => item.area_name === '总部')
    selectedProvince.value = headquarters ? headquarters.province_code : provinceOptions.value[0].province_code
  }
})


// Server选项
const serverOptions = [
  { label: '开发环境', value: Server.DEV },
  { label: '测试环境', value: Server.TEST },
  { label: '生产环境', value: Server.PROD }
]

// Role选项
const roleOptions = [
  { label: '用户', value: Role.USER },
  { label: '专家', value: Role.EXPERT }
]

// ============ 状态 ============
const inputMessage = ref('')
const messages = ref<{ role: 'user' | 'assistant'; content: string }[]>([])
// 存放每条消息的已渲染 HTML（仅助手消息使用）
const renderedContent = ref<string[]>([])
const isLoading = ref(false)
const chatContainer = ref<HTMLElement>()

// 打字机相关
// 待输出队列（把 SSE 收到的文本先塞进来）
const typingQueue = ref('')
// 定时器 id
const typingTimer = ref<number | null>(null)
// 当前正在打字的消息索引（助手消息）
const typingIndex = ref<number | null>(null)

// 状态映射
const stateMap = ref<Record<number, CurrentState>>({})
// 引用映射
const referenceMap = ref<Record<number, Reference>>({})
// 意图映射
const intentMap = ref<Record<number, Intent>>({})

// ============ marked 配置（v5 用插件） ============
marked.use(
    markedHighlight({
      langPrefix: 'hljs language-',
      highlight(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          return hljs.highlight(code, { language: lang }).value
        }
        return hljs.highlightAuto(code).value
      }
    })
)
marked.setOptions({
  gfm: true,
  breaks: true,
  // 防止特殊字符被转义
  mangle: false
})

// ============ 发送消息 ============
const sendMessage = async () => {
  if (!inputMessage.value.trim()) {
    ElMessage.warning('请输入消息内容')
    return
  }
  if (isLoading.value) {
    ElMessage.warning('正在发送中，请稍候...')
    return
  }

  const userMessage = inputMessage.value.trim()
  messages.value.push({ role: 'user', content: userMessage })
  inputMessage.value = ''
  isLoading.value = true
  await nextTick()
  scrollToBottom()

  // 新增助手消息的索引编号
  const assistantIdx = messages.value.length

  try {
    const requestData: ChatTestRequest = {
      query: userMessage,
      mode: 'both',
      server: selectedServer.value,
      province_code: selectedProvince.value,
      city_code: selectedProvince.value, // city_code赋值为province_code
      role: selectedRole.value
    }

    messages.value.push({ role: 'assistant', content: '' })
    renderedContent.value[assistantIdx] = ''
    typingIndex.value = assistantIdx

    // 启动 SSE
    await sendChatTestSSE(requestData, {
      onStart: () => {
        // 初始化信息
        stateMap.value[assistantIdx] = {
          status: Status.NEW,
          intent_status: SubStatus.NEW,
          qa_status: SubStatus.NEW,
          interrupt_status: InterruptStatus.NOT
        }
        referenceMap.value[assistantIdx] = null
        intentMap.value[assistantIdx] = null
      },
      onData: (block: Block) => {
        if(block.source === BlockSource.INTENT) {
          onIntentBlockReceived(assistantIdx, block)
        } else if (block.source === BlockSource.QA) {
          onQABlockReceived(assistantIdx, block)
        } else if (block.source === BlockSource.RISK) {
          onRiskBlockReceived(assistantIdx, block)
        } else {
          // 抛弃不用
        }
      },
      onError: (error: Error) => {
        ElMessage.error(`${error.message}`)
        stateMap.value[assistantIdx].status = Status.ERROR
      },
      onComplete: () => {
        updateOverallStatus(assistantIdx, true)
        isLoading.value = false
      }
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
    isLoading.value = false
    // 说明消息没插入记录
    if(messages.value.length == assistantIdx) {
      messages.value.push({ role: 'assistant', content: '发送消息失败' })
    }
    if(!(assistantIdx in stateMap.value)) {
      stateMap.value[assistantIdx] = {
        status: Status.ERROR,
        intent_status: SubStatus.NEW,
        qa_status: SubStatus.NEW,
        interrupt_status: InterruptStatus.NOT
      }
    } else {
      stateMap.value[assistantIdx].status = Status.ERROR
    }
    if (!(assistantIdx in referenceMap.value)) {
      referenceMap.value[assistantIdx] = null
    }
    if (!(assistantIdx in intentMap.value)) {
      intentMap.value[assistantIdx] = null
    }
  }
}

const updateOverallStatus = (index: number, finalize = false) => {
  const state = stateMap.value[index]

  // 风险优先
  if (state.status === Status.RISK) return

  const qaDone = state.qa_status === SubStatus.DONE
  let intentDone = state.intent_status === SubStatus.DONE

  if ((qaDone && intentDone) || (finalize && qaDone && (state.intent_status == SubStatus.NEW))) {
    state.status = Status.DONE
  } else {
    state.status = Status.PROCESSING
  }
}

const onIntentBlockReceived = (index: number, block: Block) => {
  const state = stateMap.value[index]

  // 如果是终态块，直接忽略
  if ([Status.DONE, Status.ERROR, Status.RISK, Status.INTERRUPTED].includes(state.status)) return

  // 第一个Intent块
  if (state.intent_status === SubStatus.NEW) {
    state.status = Status.PROCESSING
    state.intent_status = SubStatus.PROCESSING
  }

  if (block.finished) {
    state.intent_status = SubStatus.DONE
    // finished 的块才会有 extra
    if (block.extra) {
      intentMap.value[index] = block.extra as IntentRecommend
    }
  }

  updateOverallStatus(index)
}

const onQABlockReceived = (index: number, block: Block) => {
  const state = stateMap.value[index]

  if ([Status.DONE, Status.ERROR, Status.RISK, Status.INTERRUPTED].includes(state.status)) return

  // 第一个QA块
  if (state.qa_status === SubStatus.NEW) {
    state.status = Status.PROCESSING
    state.qa_status = SubStatus.PROCESSING
  }

  if (block.message?.content) {
    typingQueue.value += block.message.content
    startTypewriter()
  }

  if (block.finished) {
    state.qa_status = SubStatus.DONE
    if (block.extra) {
      referenceMap.value[index] = {mode: block.generation_mode, ref: block.extra as BlockReference}
    }
  }

  updateOverallStatus(index)
}

const onRiskBlockReceived = (index: number, _: Block) => {
  const state = stateMap.value[index]

  // 风险块直接覆盖状态，风险状态级别最高
  state.status = Status.RISK
}

// ============ 打字机（逐字渲染） ============
const startTypewriter = () => {
  if (typingTimer.value !== null) return
  if (typingIndex.value === null) return

  // 每 tick 输出 2～4 个字符，既有“打字感”又更顺滑
  const CHUNK_MIN = 2
  const CHUNK_MAX = 4
  const INTERVAL = 12 // ms

  typingTimer.value = window.setInterval(() => {
    if (!typingQueue.value.length) {
      // 队列空了，关定时器，等待下一段
      if (typingTimer.value !== null) {
        clearInterval(typingTimer.value)
        typingTimer.value = null
      }
      return
    }

    const take = Math.min(
        typingQueue.value.length,
        Math.floor(Math.random() * (CHUNK_MAX - CHUNK_MIN + 1)) + CHUNK_MIN
    )
    const piece = typingQueue.value.slice(0, take)
    typingQueue.value = typingQueue.value.slice(take)

    const idx = typingIndex.value!
    messages.value[idx].content += piece
    renderedContent.value[idx] = marked.parse(messages.value[idx].content) as string


    nextTick(() => scrollToBottom())
  }, INTERVAL)
}

onBeforeUnmount(() => {
  if (typingTimer.value !== null) clearInterval(typingTimer.value)
})

// ============ 滚动到底部 ============
const scrollToBottom = () => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

// 处理回车发送
const handleEnter = () => {
  if (inputMessage.value.trim()) {
    sendMessage()
  }
}

// Shift+Enter 插入换行（其实默认就是换行，这里写不写都行）
// 如果想保留默认行为，可以不用写
const handleShiftEnter = () => {
}

</script>

<template>
  <div class="chat-container">
    <!-- 顶部配置区域 -->
    <div class="chat-config">
      <div class="config-row">
        <div class="config-item">
          <label>服务环境：</label>
          <el-select v-model="selectedServer" placeholder="选择服务环境" size="small">
            <el-option
              v-for="option in serverOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <div class="config-item">
          <label>省分：</label>
          <el-select v-model="selectedProvince" placeholder="选择省分" size="small" filterable>
            <el-option
              v-for="province in provinceOptions"
              :key="province.province_code"
              :label="province.area_name"
              :value="province.province_code"
            />
          </el-select>
        </div>

        <div class="config-item">
          <label>角色：</label>
          <el-select v-model="selectedRole" placeholder="选择角色" size="small">
            <el-option
              v-for="option in roleOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 聊天内容 -->
    <div ref="chatContainer" class="chat-content">
      <div class="chat-messages">
        <template v-for="(msg, index) in messages" :key="index">
          <UserMessage v-if="msg.role === 'user'" :content="msg.content" />
          <AssistantMessage
              v-else
              :content="renderedContent[index]"
              :intent="intentMap[index]"
              :reference="referenceMap[index]"
              :state="stateMap[index]"
          />
        </template>
      </div>
    </div>

    <!-- 底部输入区：居中 50%，按钮与输入框等高 -->
    <div class="chat-input">
      <div class="input-container">
        <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="1"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="这里输入消息内容"
            :disabled="isLoading"
            resize="none"
            @keyup.enter.exact.prevent="handleEnter"
            @keyup.shift.enter="handleShiftEnter"
        />
        <el-button
            type="primary"
            class="send-button"
            :loading="isLoading"
            :disabled="!inputMessage.trim()"
            @click="sendMessage"
        >
          {{ isLoading ? '发送中' : '发送消息' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  /* 交给父容器决定 */
  height: 100%;
  background: #ffffff;
  /* 默认字体黑色 */
  color: #000000;
}

/* 顶部配置区域 */
.chat-config {
  background: #ffffff;
  border-bottom: 1px solid #f4f4f4;
  padding: 16px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-row {
  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item label {
  font-size: 14px;
  color: #000000;
  font-weight: 500;
  white-space: nowrap;
}

.config-item .el-select {
  width: 140px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-config {
    padding: 12px 16px;
  }

  .config-row {
    gap: 16px;
    justify-content: flex-start;
  }

  .config-item .el-select {
    width: 120px;
  }

  .config-item label {
    font-size: 13px;
  }
}

/* 内容区：只它滚动 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  /* 浅灰色，可换成 #ffffff 白色 */
  background: #ffffff;
  /* 避免 flex 子项撑爆父容器 */
  min-height: 0;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 50%;
  /* 居中消息列 */
  margin: 0 auto;
}

/* 底部输入区：居中 50%，按钮与输入框等高 */
.chat-input {
  /* 改为白色背景 */
  background: #ffffff;
  border-top: 0 solid #ffffff;
  /* 原 12px，1.5倍 */
  padding: 18px 0;
  display: flex;
  justify-content: center;
}

.input-container {
  display: flex;
  width: 50%;
  gap: 8px;
  /* 子项等高 */
  align-items: stretch;
}

/* 输入框：白底黑字，灰色边框 */
:deep(.el-textarea__inner) {
    background: transparent;
    color: #000000;
    /* 灰色边框 */
    border: 1px solid transparent;
    border-radius: 20px;
    caret-color: #000000;
}
:deep(.el-textarea__inner::placeholder) {
  color: #666666;
}

/* 发送按钮：深黑色，悬浮略浅，高度放大 */
.send-button {
  align-self: stretch;
  /* 自适应填满容器高度 */
  height: auto;
  border-radius: 20px;
  padding: 0 20px;
  /* 深黑色 */
  background: #000000;
  border-color: #000000;
  color: #ffffff;
}
.send-button:hover,
.send-button:focus {
  /* 悬浮略浅 */
  background: midnightblue;
  border-color: midnightblue;
}

/* 自定义滚动条 */
.chat-content::-webkit-scrollbar {
  width: 8px;
}
.chat-content::-webkit-scrollbar-thumb {
  background: #565869;
  border-radius: 4px;
}
</style>
