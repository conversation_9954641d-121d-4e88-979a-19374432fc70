<template>
  <div class="message user-message">
    <div class="bubble">{{ content }}</div>
    <div class="op-list">
      <QCopyIcon/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from "vue";
import { ElMessage } from 'element-plus';
import QCopyIcon from "@/components/icon/QCopyIcon.vue";

interface Props {
  content: string
}

const props = defineProps<Props>()
const { content } = toRefs(props)

const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(content.value)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    console.error("复制失败", err)
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.user-message {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 8px;
  position: relative;
}

.user-message .bubble {
  max-width: 100%;
  background: #f4f4f4;
  color: #000;
  padding: 10px 16px;
  border-radius: 14px;
  white-space: pre-wrap;
}

/* 操作列表右对齐，保留空间 */
.op-list {
  margin-top: 10px; /* 修改为 10px */
  align-self: flex-end;
  visibility: hidden; /* 默认隐藏 */
}

/* 悬浮消息时显示图标 */
.user-message:hover .op-list {
  visibility: visible;
}

.copy-icon {
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: color 0.2s;
}
.copy-icon:hover {
  color: #000;
}
</style>
