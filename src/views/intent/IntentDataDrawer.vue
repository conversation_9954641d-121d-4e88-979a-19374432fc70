<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑意图数据' : '新增意图数据'"
    direction="rtl"
    size="50%"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="drawer-form"
    >
      <el-form-item label="内容" prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="4"
          placeholder="请输入意图数据内容"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="关键词" prop="keywords">
        <div class="keywords-container">
          <el-input
            v-model="keywordInput"
            placeholder="输入关键词后按回车添加"
            @keyup.enter="addKeyword"
            class="keyword-input"
          />
          <div class="keywords-list">
            <el-tag
              v-for="(keyword, index) in keywordsList"
              :key="index"
              closable
              @close="removeKeyword(index)"
              class="keyword-tag"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="数据源" prop="data_source">
        <el-input
          v-model="formData.data_source"
          placeholder="请输入数据源"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="来源信息" prop="source_info">
        <el-input
          v-model="formData.source_info"
          type="textarea"
          :rows="3"
          placeholder="请输入来源信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="提供者" prop="provider">
        <el-input
          v-model="formData.provider"
          placeholder="请输入提供者"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
          <el-option label="生效中" :value="Status.VALID" />
          <el-option label="已失效" :value="Status.INVALID" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  createIntentData,
  updateIntentData,
  deleteIntentData,
  type IntentDataItem,
  type CreateIntentDataRequest,
  type UpdateIntentDataRequest,
  Status
} from '@/api/intent.js'
import { SUCCESS_CODE } from '@/api/common.js'

// Props
interface Props {
  visible: boolean
  data?: IntentDataItem | null
  isEdit?: boolean
  intentId?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: null,
  isEdit: false,
  intentId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// Refs
const formRef = ref<FormInstance>()
const loading = ref(false)
const keywordInput = ref('')
const keywordsList = ref<string[]>([])

// Form data
const formData = reactive<CreateIntentDataRequest>({
  intent_id: '',
  content: '',
  keywords: {},
  data_source: '',
  source_info: '',
  provider: '',
  status: Status.VALID
})

// Computed
const keywordsObject = computed(() => {
  const obj: Record<string, any> = {}
  keywordsList.value.forEach((keyword, index) => {
    obj[`keyword_${index}`] = keyword
  })
  return obj
})

// Form rules
const formRules: FormRules = {
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '长度在 1 到 1000 个字符', trigger: 'blur' }
  ],
  data_source: [
    { required: true, message: '请输入数据源', trigger: 'blur' },
    { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' }
  ],
  source_info: [
    { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
  ],
  provider: [
    { required: true, message: '请输入提供者', trigger: 'blur' },
    { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// Watch visible prop
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// Watch data prop
watch(() => props.data, (newVal) => {
  if (newVal && props.visible) {
    initForm()
  }
})

// Initialize form
const initForm = () => {
  nextTick(() => {
    if (props.isEdit && props.data) {
      // Edit mode - populate form with existing data
      Object.assign(formData, {
        intent_id: props.data.intent_id,
        content: props.data.content,
        keywords: props.data.keywords,
        data_source: props.data.data_source,
        source_info: props.data.source_info,
        provider: props.data.provider,
        status: props.data.status as Status
      })
      
      // Parse keywords from object to array
      keywordsList.value = []
      if (props.data.keywords && typeof props.data.keywords === 'object') {
        Object.values(props.data.keywords).forEach(keyword => {
          if (typeof keyword === 'string' && keyword.trim()) {
            keywordsList.value.push(keyword.trim())
          }
        })
      }
    } else {
      // Create mode - reset form
      Object.assign(formData, {
        intent_id: props.intentId || '',
        content: '',
        keywords: {},
        data_source: '',
        source_info: '',
        provider: '',
        status: Status.VALID
      })
      keywordsList.value = []
    }
    keywordInput.value = ''
    formRef.value?.clearValidate()
  })
}

// Add keyword
const addKeyword = () => {
  const keyword = keywordInput.value.trim()
  if (keyword && !keywordsList.value.includes(keyword)) {
    keywordsList.value.push(keyword)
    keywordInput.value = ''
  }
}

// Remove keyword
const removeKeyword = (index: number) => {
  keywordsList.value.splice(index, 1)
}

// Handle close
const handleClose = () => {
  emit('update:visible', false)
}

// Handle submit
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // Update keywords in form data
    formData.keywords = keywordsObject.value

    if (props.isEdit && props.data) {
      // Update intent data
      const updateData: UpdateIntentDataRequest = {
        intent_id: formData.intent_id,
        content: formData.content,
        keywords: formData.keywords,
        data_source: formData.data_source,
        source_info: formData.source_info,
        provider: formData.provider,
        status: formData.status
      }
      const response = await updateIntentData(String(props.data.intent_data_id), updateData)
      
      if (response.code === SUCCESS_CODE) {
        ElMessage.success('更新成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } else {
      // Create intent data
      const response = await createIntentData(formData)
      
      if (response.code === SUCCESS_CODE) {
        ElMessage.success('创建成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '创建失败')
      }
    }
  } catch (error) {
    console.error('Submit error:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// Handle delete (exposed method)
const handleDelete = async (intentDataItem: IntentDataItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除此意图数据吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteIntentData(String(intentDataItem.intent_data_id))
    
    if (response.code === SUCCESS_CODE) {
      ElMessage.success('删除成功')
      emit('success')
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete error:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// Expose methods
defineExpose({
  handleDelete
})
</script>

<style scoped>
.drawer-form {
  padding: 20px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.keywords-container {
  width: 100%;
}

.keyword-input {
  width: 100%;
  margin-bottom: 10px;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  margin: 2px;
}
</style>
