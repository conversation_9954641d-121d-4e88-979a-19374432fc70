<template>
  <div class="main-content">
    <!-- 快速导航 -->
    <div class="quick-nav sticky-nav">
      <div class="quick-nav-row">
        <h3 class="quick-nav-title">🚀 快速导航</h3>
        <el-button-group class="quick-button-group">
          <el-button class="nav-btn nav-btn-blue" @click="scrollToSection('category')">
            <el-icon size="18"><Folder /></el-icon>
            <span>意图类别</span>
          </el-button>
          <el-button class="nav-btn nav-btn-green" @click="scrollToSection('intent')">
            <el-icon size="18"><Collection /></el-icon>
            <span>意图管理</span>
          </el-button>
          <el-button class="nav-btn nav-btn-orange" @click="scrollToSection('intentData')">
            <el-icon size="18"><Document /></el-icon>
            <span>意图数据</span>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <div class="scroll-body">
      <div class="content-scroll-wrapper">
      <!-- 意图类别管理 -->
      <el-card ref="categorySection" class="block-card">
        <div class="header-section">
          <div class="title-section">
            <i class="el-icon-folder-opened"></i>
            <span class="main-title">意图类别管理</span>
          </div>
          <el-button class="collapse-btn" type="text" @click="categoryCollapsed = !categoryCollapsed">
            <i :class="categoryCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            <span v-if="categoryCollapsed">展开</span>
            <span v-else>收起</span>
          </el-button>
        </div>
        <el-form v-show="!categoryCollapsed" :model="categorySearchForm" inline label-width="100px" style="margin-top: 10px;">
          <el-form-item label="类别名称">
            <el-input v-model="categorySearchForm.name" placeholder="请输入分类名称" />
          </el-form-item>
          <el-form-item class="select-option" label="适用范围">
            <el-select v-model="categorySearchForm.scope" clearable @change="handleScopeChange">
              <el-option label="不限" :value="-1" />
              <el-option label="全国" :value="Scope.GLOBAL" />
              <el-option label="省分" :value="Scope.PROVINCE" />
              <el-option label="地市" :value="Scope.CITY" />
            </el-select>
          </el-form-item>

          <!-- 省份选择框 - 当scope为省分或地市时显示 -->
          <el-form-item
            v-if="categorySearchForm.scope === Scope.PROVINCE || categorySearchForm.scope === Scope.CITY"
            class="select-option"
            label="省分"
          >
            <el-select
              v-model="categorySearchForm.province_code"
              filterable
              placeholder="请选择省分"
              @change="handleProvinceChange"
            >
              <el-option label="全部" value="all" />
              <el-option
                v-for="province in provinceOptions"
                :key="province.province_code"
                :label="province.area_name"
                :value="province.province_code"
              />
            </el-select>
          </el-form-item>

          <!-- 城市选择框 - 当scope为地市时显示 -->
          <el-form-item
            v-if="categorySearchForm.scope === Scope.CITY"
            class="select-option"
            label="城市"
          >
            <el-select
              v-model="categorySearchForm.city_code"
              filterable
              placeholder="请选择城市"
              :disabled="categorySearchForm.province_code === 'all'"
            >
              <el-option label="全部" value="all" />
              <el-option
                v-for="city in cityOptions"
                :key="city.city_code"
                :label="city.area_name"
                :value="city.city_code"
              />
            </el-select>
          </el-form-item>

          <el-form-item class="select-option" label="状态">
            <el-select v-model="categorySearchForm.status" clearable>
              <el-option label="不限" value="all" />
              <el-option label="生效中" :value="Status.VALID" />
              <el-option label="已失效" :value="Status.INVALID" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleCategorySearch">查询</el-button>
            <el-button @click="handleCategoryReset">重置</el-button>
            <el-button type="success" @click="handleCreateCategory">新增</el-button>
          </el-form-item>
        </el-form>
        <el-card v-show="!categoryCollapsed" class="list-card" shadow="never">
          <el-table
            :data="categoryList"
            :row-class-name="categoryRowClassName"
            border
            highlight-current-row
            style="width: 100%;"
            @row-click="handleCategoryRowSelect"
          >
            <el-table-column :index="zeroIndexMethod" align="center" label="编号" type="index" width="80" />
            <el-table-column label="分类名称" prop="name" />
            <el-table-column label="描述" prop="desc" show-overflow-tooltip />
            <el-table-column label="适用范围" prop="scope">
              <template #default="scope">
                <el-tag :type="scope.row.scope === Scope.GLOBAL ? 'info' : (scope.row.scope === Scope.PROVINCE ? 'primary' : 'warning')">
                  {{ formatScope(scope.row.scope) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="省分名称" prop="province_code">
              <template #default="item">
                {{ formatAreaName(item.row.scope, item.row.province_code, true) }}
              </template>
            </el-table-column>
            <el-table-column label="地市名称" prop="city_code">
              <template #default="item">
                {{ formatAreaName(item.row.scope, item.row.city_code, false) }}
              </template>
            </el-table-column>
            <el-table-column label="目标角色" prop="target_role">
              <template #default="scope">
                <el-tag :type="scope.row.target_role === TargetRole.EXPERT ? 'success' : 'warning'">
                  {{ formatTargetRole(scope.row.target_role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="status">
              <template #default="scope">
                <el-tag :type="scope.row.status === Status.VALID ? 'success' : 'danger'">
                  {{ formatStatus(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="create_date" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleEditCategory(scope.row)">修改</el-button>
                <el-button link type="danger" @click="handleDeleteCategory(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-model:current-page="categoryPagination.page"
            v-model:page-size="categoryPagination.pageSize"
            :total="categoryPagination.total"
            class="pagination"
            layout="total, prev, pager, next, jumper"
            @current-change="handleCategoryPageChange"
            @size-change="handleCategorySizeChange"
          />
        </el-card>
      </el-card>

      <!-- 意图管理 -->
      <el-card ref="intentSection" class="block-card">
        <div class="header-section">
          <div class="title-section">
            <i class="el-icon-collection"></i>
            <span class="main-title">意图管理</span>
          </div>
          <el-button class="collapse-btn" type="text" @click="intentCollapsed = !intentCollapsed">
            <i :class="intentCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            <span v-if="intentCollapsed">展开</span>
            <span v-else>收起</span>
          </el-button>
        </div>
        <el-form v-show="!intentCollapsed" :model="intentSearchForm" inline label-width="100px" style="margin-top: 10px;">
          <el-form-item label="意图名称">
            <el-input v-model="intentSearchForm.intent_name" placeholder="请输入意图名称" />
          </el-form-item>
          <el-form-item class="select-option" label="意图级别">
            <el-select v-model="intentSearchForm.intent_level" clearable>
              <el-option label="不限" :value="-1" />
              <el-option :value="IntentLevel.LEVEL_1" label="一级意图" />
              <el-option :value="IntentLevel.LEVEL_2" label="二级意图" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="intentSearchForm.intent_level === IntentLevel.LEVEL_2" label="父意图">
            <el-input
              v-model="parentIntentInput"
              clearable
              @clear="clearParentIntent"
              placeholder="请输入父意图名称"
              @blur="handleParentIntentBlur"
              @input="parentIntentSelected = null"
            />
            <el-dialog v-model="parentIntentDialogVisible" title="选择父意图" width="500px">
              <el-table :data="parentIntentList" @row-click="handleParentIntentSelect">
                <el-table-column label="意图名称" prop="intent_name" />
                <el-table-column label="描述" prop="intent_desc" />
              </el-table>
              <span slot="footer" class="dialog-footer">
                <el-button @click="parentIntentDialogVisible = false">取消</el-button>
              </span>
            </el-dialog>
          </el-form-item>
          <el-form-item class="select-option" label="状态">
            <el-select v-model="intentSearchForm.status" clearable>
              <el-option label="不限" value="all" />
              <el-option :value="Status.VALID" label="生效中" />
              <el-option :value="Status.INVALID" label="已失效" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleIntentSearch">查询</el-button>
            <el-button @click="handleIntentReset">重置</el-button>
            <el-button type="success" @click="handleCreateIntent">新增</el-button>
          </el-form-item>
        </el-form>
        <el-card v-show="!intentCollapsed" class="list-card" shadow="never">
          <el-table
            :data="intentList"
            :row-class-name="intentRowClassName"
            @row-click="handleIntentRowSelect"
            border
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column :index="oneIndexMethod" align="center" label="编号" type="index" width="80" />
            <el-table-column label="意图名称" prop="intent_name" />
            <el-table-column label="描述" prop="intent_desc" show-overflow-tooltip />
            <el-table-column label="意图级别" prop="intent_level">
              <template #default="scope">
                <el-tag :type="scope.row.intent_level === IntentLevel.LEVEL_1 ? 'primary' : 'success'">
                  {{ formatIntentLevel(scope.row.intent_level) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="父意图" prop="parent_name" />
            <el-table-column label="状态" prop="status">
              <template #default="scope">
                <el-tag :type="scope.row.status === Status.VALID ? 'success' : 'danger'">
                  {{ formatStatus(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="create_date" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleEditIntent(scope.row)">编辑</el-button>
                <el-button link type="danger" @click="handleDeleteIntent(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-model:current-page="intentPagination.page"
            v-model:page-size="intentPagination.pageSize"
            :total="intentPagination.total"
            class="pagination"
            layout="total, prev, pager, next, jumper"
            @current-change="handleIntentPageChange"
            @size-change="handleIntentSizeChange"
          />
        </el-card>
      </el-card>
      <!-- 意图数据管理 -->
      <el-card ref="intentDataSection" class="block-card">
        <div class="header-section">
          <div class="title-section">
            <i class="el-icon-collection"></i>
            <span class="main-title">意图数据管理</span>
          </div>
          <el-button class="collapse-btn" type="text" @click="intentDataCollapsed = !intentDataCollapsed">
            <i :class="intentDataCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            <span v-if="intentDataCollapsed">展开</span>
            <span v-else>收起</span>
          </el-button>
        </div>
        <el-form v-show="!intentDataCollapsed" :model="intentSearchForm" inline label-width="100px" style="margin-top: 10px;">
          <el-form-item label="内容关键字">
            <el-input v-model="intentDataSearchForm.content" placeholder="请输入内容关键字" />
          </el-form-item>
          <el-form-item class="select-option" label="状态">
            <el-select v-model="intentDataSearchForm.status" clearable>
              <el-option label="不限" value="all" />
              <el-option :value="Status.VALID" label="生效中" />
              <el-option :value="Status.INVALID" label="已失效" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleIntentDataSearch">查询</el-button>
            <el-button @click="handleIntentDataReset">重置</el-button>
            <el-button type="success" @click="handleCreateIntentData">新增</el-button>
          </el-form-item>
        </el-form>
        <el-card v-show="!intentDataCollapsed" class="list-card" shadow="never">
          <el-table
              :data="intentDataList"
              border
              highlight-current-row
              style="width: 100%;"
          >
            <el-table-column :index="oneIndexMethod" align="center" label="编号" type="index" width="80" />
            <el-table-column label="内容" prop="content" min-width="200">
              <template #default="scope">
                <div class="content-cell" :title="scope.row.content">
                  {{ scope.row.content }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="status">
              <template #default="scope">
                <el-tag :type="scope.row.status === Status.VALID ? 'success' : 'danger'">
                  {{ formatStatus(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" prop="create_date" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="handleEditIntentData(scope.row)">编辑</el-button>
                <el-button link type="danger" @click="handleDeleteIntentData(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
              v-model:current-page="intentDataPagination.page"
              v-model:page-size="intentDataPagination.pageSize"
              :total="intentDataPagination.total"
              class="pagination"
              layout="total, prev, pager, next, jumper"
              @current-change="handleIntentDataPageChange"
              @size-change="handleIntentDataSizeChange"
          />
        </el-card>
      </el-card>
    </div>
    <div/>
  </div>

    <!-- 意图类别抽屉 -->
    <CategoryDrawer
      v-model:visible="categoryDrawerVisible"
      :data="currentCategoryData"
      :is-edit="isCategoryEdit"
      @success="handleCategorySuccess"
      ref="categoryDrawerRef"
    />

    <!-- 意图抽屉 -->
    <IntentDrawer
      v-model:visible="intentDrawerVisible"
      :data="currentIntentData"
      :is-edit="isIntentEdit"
      :category-id="currentCategoryRow?.id"
      @success="handleIntentSuccess"
      ref="intentDrawerRef"
    />

    <!-- 意图数据抽屉 -->
    <IntentDataDrawer
      v-model:visible="intentDataDrawerVisible"
      :data="currentIntentDataItem"
      :is-edit="isIntentDataEdit"
      :intent-id="currentIntentRow?.intent_id"
      @success="handleIntentDataSuccess"
      ref="intentDataDrawerRef"
    />
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, inject, computed, type Ref } from 'vue'
import {
  type CategoryData,
  getCategoryList,
  type GetCategoryRequest,
  type GetCategoryResponse,
  getIntentDataList,
  type GetIntentDataRequest,
  type GetIntentDataResponse,
  getIntentList,
  type GetIntentRequest,
  type GetIntentResponse,
  type IntentData,
  type IntentDataItem,
  IntentLevel,
  Scope,
  Status,
  TargetRole
} from '@/api/intent.js'
import {SUCCESS_CODE} from '@/api/common.js'
import CategoryDrawer from './CategoryDrawer.vue'
import IntentDrawer from './IntentDrawer.vue'
import IntentDataDrawer from './IntentDataDrawer.vue'
import { Folder, Collection, Document } from '@element-plus/icons-vue'
import { type AreaMap } from "@/api";

// ========== 地域数据 ==========
// 确保注入的是响应式对象
const areaInfo = inject<Ref<AreaMap>>('areaInfo')
if (!areaInfo) throw new Error('areaInfo 未注入')

// 省份选项 - 建议返回数组格式更适合选择器组件
const provinceOptions = computed(() => {
  // 安全校验：确保数据存在且为对象
  if (!areaInfo.value?.province || typeof areaInfo.value.province !== 'object') {
    return [];
  }

  const provinceDict = areaInfo.value.province;

  // 将字典转换为数组，只保留需要的字段
  return Object.values(provinceDict).map(item => ({
    province_code: item.province_code,
    area_name: item.area_name
  }));
});

// 城市选项
const cityOptions = computed(() => {
  const currentProvinceCode = categorySearchForm.value.province_code
  if (!currentProvinceCode || currentProvinceCode === 'all') return []

  if (!areaInfo.value?.city || typeof areaInfo.value.city !== 'object') {
    return [];
  }

  const cityDict = areaInfo.value.city

  return Object.values(cityDict).map(item => ({
    city_code: item.city_code,
    area_name: item.area_name,
    province_code: item.province_code
  })).filter(item => item.province_code === currentProvinceCode);
})

const formatAreaName = (scope: Scope, code: string, fromProvinceLabel: boolean) => {
  let name = '-'
  if (scope === Scope.GLOBAL) {
    name = '-'
  } else if (scope === Scope.PROVINCE) {
    if(fromProvinceLabel) {
      name = areaInfo.value.province?.[code]?.area_name ||  '未知'
    }
  } else if (scope === Scope.CITY) {
    if (fromProvinceLabel) {
      name = areaInfo.value.province?.[code]?.area_name ||  '未知'
    } else {
      name = areaInfo.value.city?.[code]?.area_name ||  '未知'
    }
  }
  return name
}

// ========== 快速导航 ==========
const categorySection = ref()
const intentSection = ref()
const intentDataSection = ref()
const activeSection = ref('category')

// 滚动到指定区域
const scrollToSection = (section: 'category' | 'intent' | 'intentData') => {
  activeSection.value = section
  let targetRef
  switch (section) {
    case 'category':
      targetRef = categorySection.value
      break
    case 'intent':
      targetRef = intentSection.value
      break
    case 'intentData':
      targetRef = intentDataSection.value
      break
  }

  if (targetRef) {
    targetRef.$el.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// ========== 意图类别管理 ==========
const categoryCollapsed = ref(false)

const currentCategoryRow = ref<CategoryData | null>(null)

// 抽屉相关
const categoryDrawerVisible = ref(false)
const categoryDrawerRef = ref()
const currentCategoryData = ref<CategoryData | null>(null)
const isCategoryEdit = ref(false)

const categorySearchForm = ref({
  name: '',
  scope: -1,
  province_code: 'all',
  city_code: 'all',
  status: Status.VALID as Status | string
})

const categoryList = ref<CategoryData[]>([])

const categoryPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

const zeroIndexMethod = (index: number) => index + 1

const categoryRowClassName = ({ row }: { row: CategoryData }) => {
  return currentCategoryRow.value && row.id === currentCategoryRow.value.id ? 'selected-row' : ''
}

const handleCategoryRowSelect = (row: CategoryData) => {
  if (!currentCategoryRow.value || row.id !== currentCategoryRow.value.id) {
    currentCategoryRow.value = row
    resetIntentSearchForm()
    loadIntentList()
  }
}

const formatScope = (scope: number) => {
  switch (scope) {
    case Scope.GLOBAL:
      return '全国'
    case Scope.PROVINCE:
      return '省分'
    case Scope.CITY:
      return '地市'
    default:
      return '未知'
  }
}

const formatStatus = (status: string) => {
  if (status === Status.VALID) return '生效中'
  if (status === Status.INVALID) return '已失效'
  return '未知'
}

const formatTargetRole = (role: string) => {
  switch (role) {
    case TargetRole.EXPERT:
      return '专家'
    case TargetRole.USER:
      return '用户'
    default:
      return '未知'
  }
}

const handleCategorySearch = () => {
  categoryPagination.value.page = 1
  loadCategoryList()
}

const handleCategoryReset = () => {
  categorySearchForm.value = {
    name: '',
    scope: -1,
    province_code: 'all',
    city_code: 'all',
    status: Status.VALID
  }
  categoryPagination.value.page = 1
  loadCategoryList()
}

// 处理适用范围变化
const handleScopeChange = (_scope: number) => {
  // 当范围变化时，重置省份和城市选择
  categorySearchForm.value.province_code = 'all'
  categorySearchForm.value.city_code = 'all'
}

// 处理省份变化
const handleProvinceChange = (_provinceCode: string) => {
  // 当省份变化时，重置城市选择
  categorySearchForm.value.city_code = 'all'
}

const handleCreateCategory = () => {
  currentCategoryData.value = null
  isCategoryEdit.value = false
  categoryDrawerVisible.value = true
}

const handleEditCategory = (row: CategoryData) => {
  currentCategoryData.value = row
  isCategoryEdit.value = true
  categoryDrawerVisible.value = true
}

const handleDeleteCategory = (row: CategoryData) => {
  categoryDrawerRef.value?.handleDelete(row)
}

const handleCategorySuccess = () => {
  loadCategoryList()
}

const handleCategoryPageChange = (page: number) => {
  categoryPagination.value.page = page
  loadCategoryList()
}

const handleCategorySizeChange = (size: number) => {
  categoryPagination.value.pageSize = size
  categoryPagination.value.page = 1
  loadCategoryList()
}

const loadCategoryList = async () => {
  try {
    const nameValue = categorySearchForm.value.name?.trim()
    const params: GetCategoryRequest = {
      page: categoryPagination.value.page,
      page_size: categoryPagination.value.pageSize,
      name: nameValue ? nameValue : null,
      scope: categorySearchForm.value.scope === -1 ? null : (categorySearchForm.value.scope as any),
      status: categorySearchForm.value.status === 'all' ? null : (categorySearchForm.value.status as any)
    }

    // 根据scope条件添加province_code和city_code参数
    const scope = categorySearchForm.value.scope
    if (scope === Scope.PROVINCE || scope === Scope.CITY) {
      // 省分或地市范围，需要传递province_code
      const provinceCode = categorySearchForm.value.province_code?.trim()
      if (provinceCode && provinceCode !== 'all') {
        params.province_code = provinceCode
      }
    }

    if (scope === Scope.CITY) {
      // 地市范围，需要传递city_code
      const cityCode = categorySearchForm.value.city_code?.trim()
      if (cityCode && cityCode !== 'all') {
        params.city_code = cityCode
      }
    }
    const response: GetCategoryResponse = await getCategoryList(params)
    if (response.code === SUCCESS_CODE) {
      categoryList.value = response.data.data
      categoryPagination.value.total = response.data.page.total
      // 默认选中第一行
      if (categoryList.value.length > 0) {
        currentCategoryRow.value = categoryList.value[0]
        resetIntentSearchForm()
        await loadIntentList()
      }
    }
  } catch (error) {
    console.error('Failed to load category list:', error)
  }
}

// ========== 意图管理 ==========
const intentCollapsed = ref(false)

const intentSearchForm = ref({
  intent_name: '' as string,
  intent_level: IntentLevel.LEVEL_2 as IntentLevel | number,
  status: 'all' as Status | string,
  parent_id: null as number | null
})

const intentList = ref<IntentData[]>([])

const intentPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

const currentIntentRow = ref<IntentData | null>(null)

// 意图抽屉相关
const intentDrawerVisible = ref(false)
const intentDrawerRef = ref()
const currentIntentData = ref<IntentData | null>(null)
const isIntentEdit = ref(false)

const intentRowClassName = ({ row }: { row: IntentData }) => {
  return currentIntentRow.value && row.intent_id === currentIntentRow.value.intent_id ? 'selected-row' : ''
}

const handleIntentRowSelect = (row: IntentData) => {
  if (!currentIntentRow.value || row.intent_id !== currentIntentRow.value.intent_id) {
    currentIntentRow.value = row
    resetIntentDataSearchForm()
    loadIntentDataList()
  }
}

const oneIndexMethod = (index: number) => index + 1

const formatIntentLevel = (level: number) => {
  if (level === IntentLevel.LEVEL_1) return '一级意图'
  if (level === IntentLevel.LEVEL_2) return '二级意图'
  return '未知'
}

// 父意图模糊搜索
const parentIntentInput = ref('')

const parentIntentDialogVisible = ref(false)

const parentIntentList = ref<IntentData[]>([])

const parentIntentSelected = ref<IntentData | null>(null)

const clearParentIntent = () => {
  parentIntentInput.value = ''
  parentIntentSelected.value = null
  intentSearchForm.value.parent_id = null
}

const handleParentIntentBlur = async () => {
  if (intentSearchForm.value.intent_level === IntentLevel.LEVEL_2 && parentIntentInput.value.trim()) {
    // 只在输入完毕时触发
    const params: GetIntentRequest = {
      category_id: currentCategoryRow.value ? String(currentCategoryRow.value.id) : undefined,
      intent_name: parentIntentInput.value.trim(),
      intent_level: IntentLevel.LEVEL_1,
      page: 1,
      page_size: 10
    }
    const res = await getIntentList(params)
    if (res.code === SUCCESS_CODE) {
      parentIntentList.value = res.data.data
      parentIntentDialogVisible.value = true
    }
  }
}

const handleParentIntentSelect = (row: IntentData) => {
  parentIntentSelected.value = row
  parentIntentInput.value = row.intent_name
  intentSearchForm.value.parent_id = Number(row.intent_id)
  parentIntentDialogVisible.value = false
}

const handleIntentSearch = () => {
  intentPagination.value.page = 1
  loadIntentList()
}

const handleIntentReset = () => {
  resetIntentSearchForm()
  loadIntentList()
}

function resetIntentSearchForm() {
  intentSearchForm.value = {
    intent_name: '',
    intent_level: IntentLevel.LEVEL_2,
    status: Status.VALID,
    parent_id: null
  }
  parentIntentInput.value = ''
  parentIntentSelected.value = null
}

const handleCreateIntent = () => {
  if (!currentCategoryRow.value) {
    return
  }
  currentIntentData.value = null
  isIntentEdit.value = false
  intentDrawerVisible.value = true
}

const handleEditIntent = (row: IntentData) => {
  currentIntentData.value = row
  isIntentEdit.value = true
  intentDrawerVisible.value = true
}

const handleDeleteIntent = (row: IntentData) => {
  intentDrawerRef.value?.handleDelete(row)
}

const handleIntentSuccess = () => {
  loadIntentList()
}

const handleIntentPageChange = (page: number) => {
  intentPagination.value.page = page
  loadIntentList()
}

const handleIntentSizeChange = (size: number) => {
  intentPagination.value.pageSize = size
  intentPagination.value.page = 1
  loadIntentList()
}

const loadIntentList = async () => {
  if (!currentCategoryRow.value) {
    intentList.value = []
    intentPagination.value.total = 0
    return
  }
  try {
    const params: GetIntentRequest = {
      page: intentPagination.value.page,
      page_size: intentPagination.value.pageSize,
      category_id: String(currentCategoryRow.value.id),
      intent_name: intentSearchForm.value.intent_name?.trim() || undefined,
      intent_level: intentSearchForm.value.intent_level === -1 ? null : (intentSearchForm.value.intent_level as any),
      status: intentSearchForm.value.status === 'all' ? null : (intentSearchForm.value.status as any),
      parent_id: intentSearchForm.value.parent_id
    }
    const response: GetIntentResponse = await getIntentList(params)
    if (response.code === SUCCESS_CODE) {
      intentList.value = response.data.data
      intentPagination.value.total = response.data.page.total

      // 默认选中第一行
      if (intentList.value.length > 0) {
        currentIntentRow.value = intentList.value[0]
        resetIntentDataSearchForm()
        await loadIntentDataList()
      }
    }
  } catch (error) {
    console.error('Failed to load intent list:', error)
  }
}

// 意图数据管理
const intentDataCollapsed = ref(false)

const intentDataList = ref<IntentDataItem[]>([])

const intentDataSearchForm = ref({
  content: '',
  status: Status.VALID as Status | string,
  intent_id: null as number | null
})

const intentDataPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
})

// 意图数据抽屉相关
const intentDataDrawerVisible = ref(false)
const intentDataDrawerRef = ref()
const currentIntentDataItem = ref<IntentDataItem | null>(null)
const isIntentDataEdit = ref(false)

const loadIntentDataList = async () => {
  if (!currentIntentRow.value) {
    intentDataList.value = []
    intentDataPagination.value.total = 0
    return
  }
  try {
    const params: GetIntentDataRequest = {
      page: intentDataPagination.value.page,
      page_size: intentDataPagination.value.pageSize,

      intent_id: String(currentIntentRow.value.intent_id),
      content: intentDataSearchForm.value.content?.trim() || null,
      status: intentDataSearchForm.value.status === 'all' ? null : (intentDataSearchForm.value.status as any)
    }
    const response: GetIntentDataResponse = await getIntentDataList(params)
    if (response.code === SUCCESS_CODE) {
      intentDataList.value = response.data.data
      intentDataPagination.value.total = response.data.page.total
    }
  } catch (error) {
    console.error('Failed to load intent data list:', error)
  }
}

const handleIntentDataSearch = () => {
  intentDataPagination.value.page = 1
  loadIntentDataList()
}

const handleIntentDataReset = () => {
  resetIntentDataSearchForm()
  loadIntentDataList()
}

function resetIntentDataSearchForm() {
  intentDataSearchForm.value = {
    content: '',
    status: Status.VALID,
    intent_id: null as number | null
  }
}

const handleCreateIntentData = () => {
  if (!currentIntentRow.value) {
    return
  }
  currentIntentDataItem.value = null
  isIntentDataEdit.value = false
  intentDataDrawerVisible.value = true
}

const handleEditIntentData = (row: IntentDataItem) => {
  currentIntentDataItem.value = row
  isIntentDataEdit.value = true
  intentDataDrawerVisible.value = true
}

const handleDeleteIntentData = (row: IntentDataItem) => {
  intentDataDrawerRef.value?.handleDelete(row)
}

const handleIntentDataSuccess = () => {
  loadIntentDataList()
}

const handleIntentDataPageChange = (page: number) => {
  intentDataPagination.value.page = page
  loadIntentDataList()
}

const handleIntentDataSizeChange = (size: number) => {
  intentDataPagination.value.pageSize = size
  intentDataPagination.value.page = 1
  loadIntentDataList()
}

onMounted(() => {
  loadCategoryList()
})
</script>

<style scoped>
/* 右侧主内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  height: 100%;             /* 不使用 100vh，交给外部决定是否占满视口 */
  overflow: hidden;         /* 不让 main-content 自己滚动 */
}

/* 顶部导航条（粘性） */
.sticky-nav {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #f9fafb;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

/* 滚动容器（只让这个滚） */
.scroll-body {
  margin-top: 10px;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 内部内容区域 */
.content-scroll-wrapper {
  padding: 20px 24px;
  box-sizing: border-box;
}

.quick-nav {
  padding: 20px 32px;
  background: #f9fafb;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom:10px;
}

/* 主容器为横向布局 */
.quick-nav-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
  flex-wrap: nowrap;
}

.quick-nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  white-space: nowrap;
}

/* 按钮组横向展开，不撑满，不换行 */
.quick-button-group {
  display: flex;
  gap: 12px;
  flex-wrap: nowrap;
}

/* 按钮美化 */
.nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  height: auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  white-space: nowrap;
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.nav-btn-blue {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.nav-btn-green {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.nav-btn-orange {
  background-color: #fff3e0;
  color: #ef6c00;
  border: 1px solid #ffe0b2;
}

/* 内容单元格样式 */
.content-cell {
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  line-height: 1.4;
  cursor: pointer;
}

.content-cell:hover {
  color: #409eff;
}

:deep(.selected-row) {
  background-color: #e6f7ff !important; /* 浅蓝色背景 */
  color: #1890ff; /* 文字颜色 */
  font-weight: bold; /* 加粗文字 */
}

/* 可选： hover 时的样式（与选中行区分） */
:deep(.el-table__row:hover:not(.selected-row)) {
  background-color: #f5f7fa !important;
}

.block-card {
  margin-bottom: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.08);
  padding-bottom: 8px;
}

.header-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.main-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  letter-spacing: 1px;
}

.collapse-btn {
  color: #409eff;
  font-weight: 500;
}

.collapse-btn:hover {
  color: #66b1ff;
}

.list-card {
  min-height: 500px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-top: 16px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.select-option {
  width: 200px;
}

</style>