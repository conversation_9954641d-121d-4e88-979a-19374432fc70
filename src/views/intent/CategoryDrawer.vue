<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑意图类别' : '新增意图类别'"
    direction="rtl"
    size="45%"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="drawer-form"
    >
      <el-form-item label="类别名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入类别名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="描述" prop="desc">
        <el-input
          v-model="formData.desc"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="适用范围" prop="scope">
        <el-select
          v-model="formData.scope"
          placeholder="请选择适用范围"
          style="width: 100%"
          @change="handleScopeChange"
        >
          <el-option label="全国" :value="Scope.GLOBAL" />
          <el-option label="省分" :value="Scope.PROVINCE" />
          <el-option label="地市" :value="Scope.CITY" />
        </el-select>
      </el-form-item>

      <!-- 省份选择框 - 当scope为省分或地市时显示 -->
      <el-form-item
        v-if="formData.scope === Scope.PROVINCE || formData.scope === Scope.CITY"
        label="省分"
        prop="province_code"
      >
        <el-select
          v-model="formData.province_code"
          placeholder="请选择省分"
          style="width: 100%"
          filterable
          @change="handleProvinceChange"
        >
          <el-option
            v-for="province in provinceOptions"
            :key="province.province_code"
            :label="province.area_name"
            :value="province.province_code"
          />
        </el-select>
      </el-form-item>

      <!-- 城市选择框 - 当scope为地市时显示 -->
      <el-form-item
        v-if="formData.scope === Scope.CITY"
        label="城市"
        prop="city_code"
      >
        <el-select
          v-model="formData.city_code"
          placeholder="请选择城市"
          style="width: 100%"
          filterable
          :disabled="!formData.province_code"
        >
          <el-option
            v-for="city in cityOptions"
            :key="city.city_code"
            :label="city.area_name"
            :value="city.city_code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="目标角色" prop="target_role">
        <el-select v-model="formData.target_role" placeholder="请选择目标角色" style="width: 100%">
          <el-option label="用户" :value="TargetRole.USER" />
          <el-option label="专家" :value="TargetRole.EXPERT" />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
          <el-option label="生效中" :value="Status.VALID" />
          <el-option label="已失效" :value="Status.INVALID" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import {ref, watch, nextTick, inject, computed, type Ref} from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  createCategory,
  updateCategory,
  deleteCategory,
  countIntentsByCategory,
  type CategoryData,
  Scope,
  Status,
  TargetRole
} from '@/api/intent.ts'
import { SUCCESS_CODE } from '@/api/common.ts'
import type { AreaMap } from '@/api/area.ts'

const tenantId = String(inject('tenantId'))

// 地域数据
const areaInfo = inject<Ref<AreaMap>>('areaInfo')
if (!areaInfo) throw new Error('areaInfo 未注入')

// 省份选项 - 建议返回数组格式更适合选择器组件
const provinceOptions = computed(() => {
  // 安全校验：确保数据存在且为对象
  if (!areaInfo.value?.province || typeof areaInfo.value.province !== 'object') {
    return [];
  }

  const provinceDict = areaInfo.value.province;

  // 将字典转换为数组，只保留需要的字段
  return Object.values(provinceDict).map(item => ({
    province_code: item.province_code, // 提取省份编码
    area_name: item.area_name          // 提取地区名称
  }));
});

// 城市选项
const cityOptions = computed(() => {
  const currentProvinceCode = formData.value.province_code
  if (!currentProvinceCode || currentProvinceCode === 'all') return []

  if (!areaInfo.value?.city || typeof areaInfo.value.city !== 'object') {
    return [];
  }

  const cityDict = areaInfo.value.city

  return Object.values(cityDict).map(item => ({
    city_code: item.city_code,
    area_name: item.area_name,
    province_code: item.province_code
  })).filter(item => item.province_code === currentProvinceCode);
})

// Props
interface Props {
  visible: boolean
  data?: CategoryData | null
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: null,
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// Refs
const formRef = ref<FormInstance>()
const loading = ref(false)

// Form data
const formData = ref({
  name: '',
  desc: '',
  scope: Scope.GLOBAL,
  province_code: '000000',
  city_code: '000000',
  target_role: TargetRole.USER,
  tenant_id: tenantId,
  status: Status.VALID
})

// Form rules
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入类别名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  desc: [
    { required: true, message: '请输入描述', trigger: 'blur' },
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],
  scope: [
    { required: true, message: '请选择适用范围', trigger: 'change' }
  ],
  province_code: [
    {
      validator: (_rule, value, callback) => {
        if (formData.value.scope === Scope.PROVINCE || formData.value.scope === Scope.CITY) {
          if (!value) {
            callback(new Error('请选择省分'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  city_code: [
    {
      validator: (_rule, value, callback) => {
        if (formData.value.scope === Scope.CITY) {
          if (!value) {
            callback(new Error('请选择地市'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  target_role: [
    { required: true, message: '请选择目标角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// Watch visible prop
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// Watch data prop
watch(() => props.data, (newVal) => {
  if (newVal && props.visible) {
    initForm()
  }
})

// Initialize form
const initForm = () => {
  nextTick(() => {
    if (props.isEdit && props.data) {
      // Edit mode - populate form with existing data
      formData.value.name = props.data.name
      formData.value.desc = props.data.desc
      formData.value.scope = props.data.scope
      formData.value.province_code = props.data.province_code
      formData.value.city_code = props.data.city_code
      formData.value.target_role = props.data.target_role as TargetRole
      formData.value.tenant_id = tenantId
      formData.value.status = props.data.status as Status
    } else {
      formData.value.name = ''
      formData.value.desc = ''
      formData.value.scope = Scope.GLOBAL
      formData.value.province_code = '000000'
      formData.value.city_code = '000000'
      formData.value.target_role = TargetRole.USER
      formData.value.tenant_id = tenantId
      formData.value.status = Status.VALID

      // 根据scope设置默认的省份和城市
      setDefaultAreaByScope(Scope.GLOBAL)
    }
    formRef.value?.clearValidate()
  })
}

// 根据scope设置默认的省份和城市
const setDefaultAreaByScope = (scope: number) => {
  if (scope === Scope.PROVINCE || scope === Scope.CITY) {
    // 省分或地市范围，设置默认省份为第一个
    const provinces = provinceOptions.value
    if (provinces.length > 0) {
      formData.value.province_code = provinces[0].province_code

      if (scope === Scope.CITY) {
        // 地市范围，设置默认城市为该省份的第一个城市
        nextTick(() => {
          const cities = cityOptions.value
          if (cities.length > 0) {
            formData.value.city_code = cities[0].city_code
          }
        })
      } else {
        formData.value.city_code = ''
      }
    }
  } else {
    // 全国范围，清空省份和城市
    formData.value.province_code = '000000'
    formData.value.city_code = '000000'
  }
}

// 处理适用范围变化
const handleScopeChange = (scope: number) => {
  setDefaultAreaByScope(scope)
}

// 处理省分变化
const handleProvinceChange = (_provinceCode: string) => {
  if (formData.value.scope === Scope.CITY) {
    // 地市范围，重置城市选择并设置为第一个
    nextTick(() => {
      const cities = cityOptions.value
      if (cities.length > 0) {
        formData.value.city_code = cities[0].city_code
      } else {
        formData.value.city_code = ''
      }
    })
  }
}

// Handle close
const handleClose = () => {
  emit('update:visible', false)
}

// Handle submit
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    if (props.isEdit && props.data) {
      // Update category
      const response = await updateCategory(String(props.data.id), formData.value)
      
      if (response.code === SUCCESS_CODE) {
        ElMessage.success('更新成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } else {
      // Create category
      const response = await createCategory(formData.value)
      
      if (response.code === SUCCESS_CODE) {
        ElMessage.success('创建成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '创建失败')
      }
    }
  } catch (error) {
    console.error('Submit error:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// Handle delete (exposed method)
const handleDelete = async (categoryData: CategoryData) => {
  try {
    // 先查询该类别关联的意图数量
    const response = await countIntentsByCategory(String(categoryData.id))
    
    let intentCount = 0
    if (response.code === SUCCESS_CODE) {
      intentCount = response.data
    }
    
    // 如果有关联的意图，提示用户
    if (intentCount > 0) {
      await ElMessageBox.confirm(
        `该类别"${categoryData.name}"绑定了 <span style="font-weight: bold; color: #409eff;">${intentCount}</span> 条意图，确认删除将同时删除这些意图，此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      )
    } else {
      // 没有关联意图，直接提示删除类别
      await ElMessageBox.confirm(
        `确定要删除类别"${categoryData.name}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }

    const deleteResponse = await deleteCategory(String(categoryData.id))
    
    if (deleteResponse.code === SUCCESS_CODE) {
      ElMessage.success('删除成功')
      emit('success')
    } else {
      ElMessage.error(deleteResponse.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete error:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// Expose methods
defineExpose({
  handleDelete
})
</script>

<style scoped>
.drawer-form {
  padding: 20px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
