<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="isEdit ? '编辑意图' : '新增意图'"
    direction="rtl"
    size="45%"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="drawer-form"
    >
      <el-form-item label="意图名称" prop="intent_name">
        <el-input
          v-model="formData.intent_name"
          placeholder="请输入意图名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="意图描述" prop="intent_desc">
        <el-input
          v-model="formData.intent_desc"
          type="textarea"
          :rows="3"
          placeholder="请输入意图描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="意图级别" prop="intent_level">
        <el-select 
          v-model="formData.intent_level" 
          placeholder="请选择意图级别" 
          style="width: 100%"
          @change="handleLevelChange"
        >
          <el-option label="一级意图" :value="IntentLevel.LEVEL_1" />
          <el-option label="二级意图" :value="IntentLevel.LEVEL_2" />
        </el-select>
      </el-form-item>

      <el-form-item 
        v-if="formData.intent_level === IntentLevel.LEVEL_2" 
        label="父意图" 
        prop="parent_intent_id"
      >
        <el-select
          v-model="formData.parent_intent_id"
          placeholder="请选择父意图"
          style="width: 100%"
          filterable
          remote
          :remote-method="searchParentIntents"
          :loading="parentIntentLoading"
        >
          <el-option
            v-for="intent in parentIntentOptions"
            :key="intent.intent_id"
            :label="intent.intent_name"
            :value="String(intent.intent_id)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
          <el-option label="生效中" :value="Status.VALID" />
          <el-option label="已失效" :value="Status.INVALID" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  createIntent,
  updateIntent,
  deleteIntent,
  getIntentList,
  countIntentDataByIntent,
  countSubIntents,
  type IntentData,
  type CreateIntentRequest,
  type UpdateIntentRequest,
  type GetIntentRequest,
  IntentLevel,
  Status
} from '@/api/intent.ts'
import { SUCCESS_CODE } from '@/api/common.js'

// Props
interface Props {
  visible: boolean
  data?: IntentData | null
  isEdit?: boolean
  categoryId?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  data: null,
  isEdit: false,
  categoryId: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// Refs
const formRef = ref<FormInstance>()
const loading = ref(false)
const parentIntentLoading = ref(false)
const parentIntentOptions = ref<IntentData[]>([])

// Form data
const formData = reactive<CreateIntentRequest>({
  category_id: 0,
  intent_name: '',
  intent_desc: '',
  intent_level: IntentLevel.LEVEL_1,
  parent_intent_id: 0,
  status: Status.VALID
})

// Form rules
const formRules: FormRules = {
  intent_name: [
    { required: true, message: '请输入意图名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  intent_desc: [
    { required: true, message: '请输入意图描述', trigger: 'blur' },
    { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
  ],
  intent_level: [
    { required: true, message: '请选择意图级别', trigger: 'change' }
  ],
  parent_intent_id: [
    { 
      validator: (_rule, value, callback) => {
        if (formData.intent_level === IntentLevel.LEVEL_2 && (!value || value === 0)) {
          callback(new Error('二级意图必须选择父意图'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// Watch visible prop
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// Watch data prop
watch(() => props.data, (newVal) => {
  if (newVal && props.visible) {
    initForm()
  }
})

// Initialize form
const initForm = () => {
  nextTick(() => {
    if (props.isEdit && props.data) {
      // Edit mode - populate form with existing data
      Object.assign(formData, {
        category_id: String(props.data.category_id),
        intent_name: props.data.intent_name,
        intent_desc: props.data.intent_desc,
        intent_level: props.data.intent_level as IntentLevel,
        parent_intent_id: props.data.parent_id ? String(props.data.parent_id) : 0,
        status: props.data.status as Status
      })
      
      // Load parent intent if it's level 2
      if (props.data.intent_level === IntentLevel.LEVEL_2 && props.data.parent_id) {
        parentIntentOptions.value = [{
          intent_id: props.data.parent_id,
          intent_name: props.data.parent_name,
          intent_desc: '',
          category_id: props.data.category_id,
          category_name: props.data.category_name,
          intent_level: IntentLevel.LEVEL_1,
          parent_id: '',
          parent_name: '',
          create_date: '',
          update_date: '',
          status: Status.VALID
        }]
      }
    } else {
      // Create mode - reset form
      Object.assign(formData, {
        category_id: props.categoryId ? String(props.categoryId) : 0,
        intent_name: '',
        intent_desc: '',
        intent_level: IntentLevel.LEVEL_1,
        parent_intent_id: 0,
        status: Status.VALID
      })
      parentIntentOptions.value = []
    }
    formRef.value?.clearValidate()
  })
}

// Handle level change
const handleLevelChange = (level: IntentLevel) => {
  if (level === IntentLevel.LEVEL_1) {
    formData.parent_intent_id = 0
    parentIntentOptions.value = []
  }
}

// Search parent intents
const searchParentIntents = async (query: string) => {
  if (!query || !props.categoryId) {
    parentIntentOptions.value = []
    return
  }

  try {
    parentIntentLoading.value = true
    const params: GetIntentRequest = {
      category_id: props.categoryId,
      intent_name: query,
      intent_level: IntentLevel.LEVEL_1,
      page: 1,
      page_size: 20
    }
    
    const response = await getIntentList(params)
    if (response.code === SUCCESS_CODE) {
      parentIntentOptions.value = response.data.data
    }
  } catch (error) {
    console.error('Search parent intents error:', error)
  } finally {
    parentIntentLoading.value = false
  }
}

// Handle close
const handleClose = () => {
  emit('update:visible', false)
}

// Handle submit
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    if (props.isEdit && props.data) {
      // Update intent
      const updateData: UpdateIntentRequest = {
        category_id: formData.category_id,
        intent_name: formData.intent_name,
        intent_desc: formData.intent_desc,
        intent_level: formData.intent_level,
        parent_intent_id: formData.intent_level === IntentLevel.LEVEL_2 ? formData.parent_intent_id : null,
        status: formData.status
      }
      const response = await updateIntent(String(props.data.intent_id), updateData)
      
      if (response.code === SUCCESS_CODE) {
        ElMessage.success('更新成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } else {
      // Create intent
      const createData = {
        ...formData,
        parent_intent_id: formData.intent_level === IntentLevel.LEVEL_2 ? formData.parent_intent_id : 0
      }
      const response = await createIntent(createData)
      
      if (response.code === SUCCESS_CODE) {
        ElMessage.success('创建成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '创建失败')
      }
    }
  } catch (error) {
    console.error('Submit error:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// Handle delete (exposed method)
const handleDelete = async (intentData: IntentData) => {
  try {
    // 根据意图级别查询关联数据数量
    let subIntentCount = 0
    let intentDataCount = 0
    
    if (intentData.intent_level === IntentLevel.LEVEL_1) {
      // 一级意图，查询子意图数量
      const response = await countSubIntents(String(intentData.intent_id))
      if (response.code === SUCCESS_CODE) {
        subIntentCount = response.data
      }
    } else {
      // 二级意图，查询意图数据数量
      const response = await countIntentDataByIntent(String(intentData.intent_id))
      if (response.code === SUCCESS_CODE) {
        intentDataCount = response.data
      }
    }
    
    // 根据意图级别和关联数据数量显示不同的提示
    if (intentData.intent_level === IntentLevel.LEVEL_1 && subIntentCount > 0) {
      await ElMessageBox.confirm(
        `该一级意图"${intentData.intent_name}"包含 <span style="font-weight: bold; color: #409eff;">${subIntentCount}</span> 个子意图，确认删除将同时删除这些子意图，此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      )
    } else if (intentData.intent_level === IntentLevel.LEVEL_2 && intentDataCount > 0) {
      await ElMessageBox.confirm(
        `该二级意图"${intentData.intent_name}"绑定了 <span style="font-weight: bold; color: #409eff;">${intentDataCount}</span> 条意图数据，确认删除将同时删除这些数据，此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      )
    } else {
      // 没有关联数据，直接提示删除意图
      const levelText = intentData.intent_level === IntentLevel.LEVEL_1 ? '一级' : '二级'
      await ElMessageBox.confirm(
        `确定要删除${levelText}意图"${intentData.intent_name}"吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }

    const deleteResponse = await deleteIntent(String(intentData.intent_id), intentData.intent_level)
    
    if (deleteResponse.code === SUCCESS_CODE) {
      ElMessage.success('删除成功')
      emit('success')
    } else {
      ElMessage.error(deleteResponse.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete error:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// Expose methods
defineExpose({
  handleDelete
})
</script>

<style scoped>
.drawer-form {
  padding: 20px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
