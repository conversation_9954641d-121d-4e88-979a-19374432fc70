import { requestSSE, type <PERSON><PERSON>Handler, type SSEError<PERSON>and<PERSON>, type SSECompleteHandler } from '@/utils/request.ts'

// ==================== 枚举类型定义 ====================

export enum Server {
  DEV = "dev",
  TEST = "test",
  PROD = "prod"
}

export enum Role {
  USER = "user",
  ASSISTANT = "assistant",
  SYSTEM = "system",
  EXPERT = "expert"
}

export enum Code {
  SUCCESS = 200,
  ERROR = 500
}

export enum IntentStatus {
  NO_MATCH = 0,
  FUZZY = 1,
  LOCKED = 2,
  CONFIRMED = 3
}

export enum RefType {
  FAQ = "faq",
  DOCUMENT = "document",
  HISTORY = "history"
}

export enum BlockSource {
  INTENT = "intent",
  QA = "qa",
  RISK = "risk"
}

export enum GenerationMode {
  // QA部分使用的模式
  REF_KB = "ref_kb",
  BY_LLM = "by_llm",
  DEFAULT = "default",

  // Intent部分使用的模式
  INTENT = "intent",

  // Risk部分使用的模式
  RISK_QUESTION = "risk_question",
  RISK_ANSWER = "risk_answer"
}

export enum SceneType {
  Query = "query",
  MODIFY = "modify",
  OTHER = "other"
}

// ==================== 请求类型定义 ====================

export interface ChatTestRequest {
  server?: Server
  query: string
  province_code?: string
  city_code?: string
  role?: Role
  tag_id_list?: number[]
  email?: string
  mode?: string
}

// ==================== 响应类型定义 ====================

// 消息片段
export interface ReplyMessageFragment {
  message_id: string
  created_at: string
  content: string
  role: Role
}

// 块引用
export interface ChunkReference {
  chunk_id: string
  document_id: string
  document_name: string
  kb_id: string
  ref_type: RefType
  content: string
  image_id: string
  positions: number[] | null
  extra: Extra | null
  quoted: boolean
}

// 文档引用
export interface DocumentReference {
  document_id: string
  document_name: string
  ref_type: RefType
  extra: Extra | null
  count: number
}

// 块引用信息
export interface BlockReference {
  chunk_reference_list: ChunkReference[] | null
  document_reference_list: DocumentReference[] | null
}

// 推荐工具
export interface RecommendTool {
  tool_id: string
  tool_type: string
  tool_name: string
  scene_id: string
  scene_code: string
  scene_name: string
  scene_type: SceneType
}

// 推荐项
export interface RecommendItem {
  intent_id: string
  intent_name: string
  content: string
  tool: RecommendTool | null
}

// 意图推荐
export interface IntentRecommend {
  recognized_status: IntentStatus
  recognized_intent_id: string | null
  recognized_intent_name: string | null
  content: string | null
  recommend_list: RecommendItem[]
}

// 扩展信息基类
export interface GroupExtra {
  group_id: string
}

export interface KnowledgeExtra {
  knowledge_id: string
  knowledge_type: string
  knowledge_domain: string
}

// 联合类型
export type Extra = GroupExtra | KnowledgeExtra
export type BlockExtra = BlockReference | IntentRecommend

// 主要的Block类型
export interface Block {
  code: Code
  error: string
  message: ReplyMessageFragment | null
  conversation_id: string
  group_id: string
  extra: BlockExtra | null
  source: BlockSource
  finished: boolean
  generation_mode: GenerationMode | null
}

// ==================== 响应类型定义 ====================

// SSE回调函数类型
export type ChatTestSSEHandlers = {
  onData: SSEHandler<Block>
  onError?: SSEErrorHandler
  onComplete?: SSECompleteHandler
}

// ==================== API 函数实现 ====================

/**
 * 发送聊天测试请求（流式响应）
 * 使用SSE方式接收流式数据
 */
export async function sendChatTestSSE(
  data: ChatTestRequest,
  handlers: ChatTestSSEHandlers
): Promise<void> {
  // 设置默认值
  const requestData: ChatTestRequest = {
    server: Server.DEV,
    province_code: "100000",
    city_code: "100000",
    role: Role.USER,
    tag_id_list: [],
    email: "",
    mode: "both",
    ...data
  }

  return requestSSE<Block>(
    {
      url: '/qr_test/chat',
      method: 'post',
      data: requestData
    },
    handlers.onData,
    handlers.onError,
    handlers.onComplete
  )
}