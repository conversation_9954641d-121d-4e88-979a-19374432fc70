// 通用API响应类型定义
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 定义响应数据中的分页信息类型
export interface Page {
  total: number
  current_page: number
  size: number
}

// 分页数据的定义
export interface PaginationData<T> {
  page: Page,
  data: T
}

export interface StreamChunk {
  code: number;
  error?: string;
}

// 定义成功状态码
export const SUCCESS_CODE = 200
export const ERROR_CODE = 500