import request from '@/utils/request.ts'
import type { Scope } from './intent.ts'
import type { ApiResponse } from './common.js'


export interface Area {
  area_id: string
  area_name: string
  scope: Scope
  province_code: string
  city_code: string
}

export interface AreaMap {
  global_: Area | null
  province: Record<string, Area> | null
  city: Record<string, Area> | null
}

export type GetAreaInfoResponse = ApiResponse<AreaMap>

// ==================== API 函数实现 ====================

// 地域信息 API
export async function getAreaInfo(): Promise<GetAreaInfoResponse> {
  return request<GetAreaInfoResponse>({
    url: '/area/get_area_info',
    method: 'get'
  })
}
