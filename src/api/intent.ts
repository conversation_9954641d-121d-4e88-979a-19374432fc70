import request from '@/utils/request.ts'
import type { ApiResponse, PaginationData } from './common.js'

// 定义枚举类型，与Python服务保持一致
export enum Scope {
  GLOBAL = 0,
  PROVINCE = 1,
  CITY = 2
}

export enum Status {
  INVALID = "0",
  VALID = "1"
}

export enum TargetRole {
  USER = "user",
  EXPERT = "expert"
}

export enum IntentLevel {
  LEVEL_1 = 1,
  LEVEL_2 = 2
}

// ==================== 意图类别 (IntentCategory) ====================

// 定义请求参数类型，与Python pydantic模型保持一致
export interface GetCategoryRequest {
  page?: number
  page_size?: number
  id?: string | null
  name?: string | null
  scope?: Scope | null
  province_code?: string | null
  city_code?: string | null
  target_role?: TargetRole | null
  status?: Status | null
}

// 定义响应数据中的分类数据类型，与API实际返回格式保持一致
export interface CategoryData {
  id: string  // API返回的是字符串类型
  name: string
  desc: string
  scope: number  // API返回的是数字类型
  province_code: string
  city_code: string
  target_role: string
  tenant_id: string
  create_date: string
  update_date: string
  status: string
}

// 创建意图类别请求
export interface CreateCategoryRequest {
  name: string
  desc: string
  scope: Scope
  province_code: string
  city_code: string
  target_role: TargetRole
  tenant_id: string
  status: Status
}

// 更新意图类别请求
export interface UpdateCategoryRequest {
  name?: string | null
  desc?: string | null
  scope?: Scope | null
  province_code?: string | null
  city_code?: string | null
  target_role?: TargetRole | null
  tenant_id?: string | null
  status?: Status | null
}

// 定义完整的响应数据类型，使用通用类型
export type GetCategoryResponse = ApiResponse<PaginationData<CategoryData[]>>
export type CreateCategoryResponse = ApiResponse<CategoryData>
export type GetCategoryByIdResponse = ApiResponse<CategoryData>
export type UpdateCategoryResponse = ApiResponse<CategoryData>
export type DeleteCategoryResponse = ApiResponse<string>

// ==================== 意图 (Intent) ====================

// 获取意图列表请求
export interface GetIntentRequest {
  page?: number
  page_size?: number
  intent_id?: string | null
  category_id?: string | null
  intent_name?: string | null
  intent_level?: IntentLevel | null
  parent_id?: number | null
  status?: Status | null
}

// 意图数据
export interface IntentData {
  intent_id: string
  intent_name: string
  intent_desc: string
  category_id: string
  category_name: string
  intent_level: number
  parent_id: string
  parent_name: string
  create_date: string
  update_date: string
  status: string
}

// 创建意图请求
export interface CreateIntentRequest {
  category_id: number
  intent_name: string
  intent_desc: string
  intent_level: IntentLevel
  parent_intent_id: number
  status: Status
}

// 更新意图请求
export interface UpdateIntentRequest {
  category_id?: number | null
  intent_name?: string | null
  intent_desc?: string | null
  intent_level?: IntentLevel | null
  parent_intent_id?: number | null
  status?: Status | null
}

// 意图相关响应类型
export type GetIntentResponse = ApiResponse<PaginationData<IntentData[]>>
export type CreateIntentResponse = ApiResponse<IntentData>
export type GetIntentByIdResponse = ApiResponse<IntentData>
export type UpdateIntentResponse = ApiResponse<IntentData>
export type DeleteIntentResponse = ApiResponse<string>

// ==================== 意图数据 (IntentData) ====================

// 获取意图数据列表请求
export interface GetIntentDataRequest {
  page?: number
  page_size?: number
  intent_data_id?: string | null
  intent_id?: string | null
  content?: string | null
  status?: Status | null
}

// 意图数据项
export interface IntentDataItem {
  intent_data_id: string
  intent_id: string
  intent_name: string
  content: string
  keywords: Record<string, any>
  data_source: string
  source_info: string
  provider: string
  create_date: string
  update_date: string
  status: string
}

// 创建意图数据请求
export interface CreateIntentDataRequest {
  intent_id: string
  content: string
  keywords: Record<string, any>
  data_source: string
  source_info: string
  provider: string
  status: Status
}

// 更新意图数据请求
export interface UpdateIntentDataRequest {
  intent_id?: string | null
  content?: string | null
  keywords?: Record<string, any> | null
  data_source?: string | null
  source_info?: string | null
  provider?: string | null
  status?: Status | null
}

// 意图数据相关响应类型
export type GetIntentDataResponse = ApiResponse<PaginationData<IntentDataItem[]>>
export type CreateIntentDataResponse = ApiResponse<IntentDataItem>
export type GetIntentDataByIdResponse = ApiResponse<IntentDataItem>
export type UpdateIntentDataResponse = ApiResponse<IntentDataItem>
export type DeleteIntentDataResponse = ApiResponse<string>

// ==================== API 函数实现 ====================

// 意图类别 API
export async function getCategoryList(params: GetCategoryRequest): Promise<GetCategoryResponse> {
  return request<GetCategoryResponse>({
    url: '/intent/get_category_list',
    method: 'get',
    params
  })
}

export async function createCategory(data: CreateCategoryRequest): Promise<CreateCategoryResponse> {
  return request<CreateCategoryResponse>({
    url: '/intent/create_category',
    method: 'post',
    data
  })
}

export async function getCategoryById(categoryId: string): Promise<GetCategoryByIdResponse> {
  return request<GetCategoryByIdResponse>({
    url: `/intent/get_category?category_id=${categoryId}`,
    method: 'get'
  })
}

export async function updateCategory(categoryId: string, data: UpdateCategoryRequest): Promise<UpdateCategoryResponse> {
  return request<UpdateCategoryResponse>({
    url: `/intent/update_category?category_id=${categoryId}`,
    method: 'put',
    data
  })
}

export async function deleteCategory(categoryId: string): Promise<DeleteCategoryResponse> {
  return request<DeleteCategoryResponse>({
    url: `/intent/delete_category?category_id=${categoryId}`,
    method: 'delete'
  })
}

// 意图 API
export async function getIntentList(params: GetIntentRequest): Promise<GetIntentResponse> {
  return request<GetIntentResponse>({
    url: '/intent/get_intent_list',
    method: 'get',
    params
  })
}

export async function createIntent(data: CreateIntentRequest): Promise<CreateIntentResponse> {
  return request<CreateIntentResponse>({
    url: '/intent/create_intent',
    method: 'post',
    data
  })
}

export async function getIntentById(intentId: string): Promise<GetIntentByIdResponse> {
  return request<GetIntentByIdResponse>({
    url: `/intent/get_intent?intent_id=${intentId}`,
    method: 'get'
  })
}

export async function updateIntent(intentId: string, data: UpdateIntentRequest): Promise<UpdateIntentResponse> {
  return request<UpdateIntentResponse>({
    url: `/intent/update_intent?intent_id=${intentId}`,
    method: 'put',
    data
  })
}

export async function deleteIntent(intentId: string, intentLevel: number): Promise<DeleteIntentResponse> {
  return request<DeleteIntentResponse>({
    url: `/intent/delete_intent?intent_id=${intentId}&intent_level=${intentLevel}`,
    method: 'delete'
  })
}

// 根据分类ID查询意图数量
export async function countIntentsByCategory(categoryId: string): Promise<ApiResponse<number>> {
  return request<ApiResponse<number>>({
    url: `/intent/count_intents_by_category?category_id=${categoryId}`,
    method: 'get'
  })
}

// 根据意图ID查询意图数据数量
export async function countIntentDataByIntent(intentId: string): Promise<ApiResponse<number>> {
  return request<ApiResponse<number>>({
    url: `/intent/count_intent_data_by_intent?intent_id=${intentId}`,
    method: 'get'
  })
}

// 根据父意图ID查询子意图数量
export async function countSubIntents(parentIntentId: String): Promise<ApiResponse<number>> {
  return request<ApiResponse<number>>({
    url: `/intent/count_sub_intents?parent_intent_id=${parentIntentId}`,
    method: 'get'
  })
}

// 意图数据 API
export async function getIntentDataList(params: GetIntentDataRequest): Promise<GetIntentDataResponse> {
  return request<GetIntentDataResponse>({
    url: '/intent/get_intent_data_list',
    method: 'get',
    params
  })
}

export async function createIntentData(data: CreateIntentDataRequest): Promise<CreateIntentDataResponse> {
  return request<CreateIntentDataResponse>({
    url: '/intent/create_intent_data',
    method: 'post',
    data
  })
}

export async function getIntentDataById(intentDataId: string): Promise<GetIntentDataByIdResponse> {
  return request<GetIntentDataByIdResponse>({
    url: `/intent/get_intent_data?intent_data_id=${intentDataId}`,
    method: 'get'
  })
}

export async function updateIntentData(intentDataId: string, data: UpdateIntentDataRequest): Promise<UpdateIntentDataResponse> {
  return request<UpdateIntentDataResponse>({
    url: `/intent/update_intent_data?intent_data_id=${intentDataId}`,
    method: 'put',
    data
  })
}

export async function deleteIntentData(intentDataId: string): Promise<DeleteIntentDataResponse> {
  return request<DeleteIntentDataResponse>({
    url: `/intent/delete_intent_data?intent_data_id=${intentDataId}`,
    method: 'delete'
  })
}
