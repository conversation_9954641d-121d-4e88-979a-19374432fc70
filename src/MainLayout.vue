<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="logo">{{ appTitle }}</div>
      <div class="header-right">
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index" :to="item.path">
            {{ item.name }}
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="user-info">
          <span>{{ currentUser.username }}</span>
          <el-dropdown>
            <el-button type="text" class="logout-btn">
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <div class="main-content">
      <!-- 左侧菜单 -->
      <el-aside width="200px" class="aside">
        <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical-demo"
            @select="handleMenuSelect"
            :collapse="isCollapse"
        >
          <template v-for="menu in filteredMenus" :key="menu.id">
            <el-menu-item
                v-if="!menu.children || menu.children.length === 0"
                :index="menu.path"
            >
              <el-icon>
                <component :is="menu.icon" />
              </el-icon>
              <span>{{ menu.name }}</span>
            </el-menu-item>
            <el-sub-menu
                v-else
                :index="menu.path"
            >
              <template #title>
                <el-icon>
                  <component :is="menu.icon" />
                </el-icon>
                <span>{{ menu.name }}</span>
              </template>
              <el-menu-item
                  v-for="child in menu.children"
                  :key="child.id"
                  :index="child.path"
              >
                <span>{{ child.name }}</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="main">
        <router-view />
      </el-main>
    </div>
  </div>
</template>

<script setup lang="js">
import { ref, computed, onMounted, markRaw, provide } from 'vue';  // 引入 markRaw 和 provide
import { useRouter, useRoute } from 'vue-router';
import { getAreaInfo, SUCCESS_CODE } from "@/api/index.ts";
import {
  HomeFilled as Home,
  User,
  Setting,
  ArrowDown,
  Aim,
  ChatRound
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const appTitle = import.meta.env.VITE_APP_NAME;

// 定义Props
const props = defineProps({
  currentUser: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

const areaInfo = ref({
  global_: null,
  province: {},
  city: {}
})

// 提供tenantId给子组件使用
provide('tenantId', props.currentUser.tenantId);
provide('areaInfo', areaInfo)

// 定义Emits
const emit = defineEmits(['logout']);

// 状态管理
const isCollapse = ref(false);
const breadcrumbList = ref([]);

// 路由相关
const router = useRouter();
const route = useRoute();

// 所有菜单（使用 markRaw 标记图标组件为非响应式）
const allMenus = ref([
  { id: '1', name: '首页', path: '/home', icon: markRaw(Home), roles: ['admin', 'editor', 'viewer'] },
  { id: '2', name: '用户管理', path: '/user', icon: markRaw(User), roles: ['admin', 'editor'] },
  { id: '3', name: '系统设置', path: '/setting', icon: markRaw(Setting), roles: ['admin'] },
  {
    id: '4', name: '意图管理',
    path: '/intentManagement',
    icon: markRaw(Aim),  // 使用 markRaw 处理图标组件
    roles: ['admin'],
    children: [
      { id: '4-1', name: '意图数据管理', path: '/intentManagement/data', roles: ['admin'] },
    ]
  },
  { id: '5', name: '聊天测试', path: '/chat', icon: markRaw(ChatRound), roles: ['admin'] },
]);

// 根据用户权限过滤菜单
const filteredMenus = computed(() => {
  if (!props.currentUser || !props.currentUser.roles) return [];

  return allMenus.value.filter(menu => {
    return menu.roles.some(role => props.currentUser.roles.includes(role));
  });
});

// 计算当前激活菜单
const activeMenu = computed(() => {
  const { path } = route;
  return path;
});

// 菜单选择处理
const handleMenuSelect = (index) => {
  // 避免重复跳转到当前路径
  if (route.path !== index) {
    router.push(index);
  }
};

// 登出处理
const handleLogout = () => {
  emit('logout');
  ElMessage.success('已退出登录');
};

// 更新面包屑
const updateBreadcrumb = () => {
  const matched = route.matched;
  breadcrumbList.value = matched.map((item)  => ({
    name: item.meta.title || item.name,
    path: item.path
  }));
};

// 获取地区信息
const fetchAreaInfo = async () => {
  try {
    const response = await getAreaInfo();
    if (response.code === SUCCESS_CODE) {
      areaInfo.value = response.data;
    }
  } catch (e) {
    // do-nothing
  }
}

// 初始化
onMounted(() => {
  // 监听路由变化更新面包屑
  router.afterEach(updateBreadcrumb);
  updateBreadcrumb();
  fetchAreaInfo();
});
</script>

<style scoped>
.main-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 18px;
  font-weight: bold;
  color: dimgray;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.breadcrumb {
  margin-right: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.aside {
  background-color: #fff;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  overflow-x: hidden;
}

.el-menu-vertical-demo {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
}

.main {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
  overflow-y: auto;
}

.logout-btn {
  padding: 0;
}
</style>
