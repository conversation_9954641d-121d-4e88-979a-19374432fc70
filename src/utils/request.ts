import axios from 'axios'
import { ApiResponse, SUCCESS_CODE } from "@/api";

// 创建axios实例
const service = axios.create({
  baseURL: '/admin_api', // 与vite.config.ts中的代理配置保持一致
  timeout: 5000
})

// 请求拦截器
service.interceptors.request.use(
  (config: any) => {
    // 在发送请求之前做些什么
    return config
  },
  (error: any) => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: any) => {
    // 对响应数据做点什么
    return response.data
  },
  (error: any) => {
    // 对响应错误做点什么
    return Promise.reject(error)
  }
)

// 添加泛型类型支持
const request = <T = any>(config: any): Promise<T> => {
  return service(config)
}

// SSE配置接口
export interface SSEConfig {
  url: string
  method?: 'get' | 'post' | 'put' | 'delete'
  data?: any
  params?: any
  headers?: Record<string, string>
}

// SSE响应处理函数类型
export type SSEHandler<T> = (data: T) => void
export type SSEErrorHandler = (error: Error) => void
export type SSECompleteHandler = () => void

/**
 * 发送SSE流式请求
 * @param config 请求配置
 * @param onData 数据处理回调
 * @param onError 错误处理回调
 * @param onComplete 完成处理回调
 * @returns Promise<void>
 */
export const requestSSE = async <T = any>(
  config: SSEConfig,
  onData: SSEHandler<T>,
  onError?: SSEErrorHandler,
  onComplete?: SSECompleteHandler
): Promise<void> => {
  try {
    // 构建完整的URL
    const baseURL = service.defaults.baseURL || ''
    const fullUrl = `${baseURL}${config.url}`

    // 构建请求选项
    const fetchOptions: RequestInit = {
      method: config.method?.toUpperCase() || 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        ...config.headers
      }
    }

    // 添加请求体（如果是POST等方法）
    if (config.data && ['POST', 'PUT', 'PATCH'].includes(fetchOptions.method as string)) {
      fetchOptions.body = JSON.stringify(config.data)
    }

    // 添加查询参数
    let urlWithParams = fullUrl
    if (config.params) {
      const searchParams = new URLSearchParams()
      Object.keys(config.params).forEach(key => {
        if (config.params[key] !== null && config.params[key] !== undefined) {
          searchParams.append(key, String(config.params[key]))
        }
      })
      urlWithParams = `${fullUrl}?${searchParams.toString()}`
    }

    // 发送请求
    const response = await fetch(urlWithParams, fetchOptions)

    if (!response.ok) {
      onError?.(Error(`HTTP error! status: ${response.status}`))
      return
    }

    if (!response.body) {
      onError?.(Error(`Response body is null`))
      return
    }

    // 创建读取器
    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          onComplete?.()
          break
        }

        // 解码数据
        let chunk = decoder.decode(value, { stream: false })

        // 解析SSE格式的数据
        chunk = chunk.replace("data:\s+", "")
        try {
          const data: T = JSON.parse(chunk)
          onData(data)
        } catch (error) {
          console.error('Failed to parse SSE data:', error)
          onError?.(error as Error)
        }
      }
    } finally {
      reader.releaseLock()
    }
  } catch (error) {
    console.error('SSE request failed:', error)
    onError?.(error as Error)
  }
}

export default request