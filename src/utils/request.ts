import axios from 'axios'

// 创建axios实例
const service = axios.create({
  baseURL: '/admin_api', // 与vite.config.ts中的代理配置保持一致
  timeout: 5000
})

// 请求拦截器
service.interceptors.request.use(
  (config: any) => {
    // 在发送请求之前做些什么
    return config
  },
  (error: any) => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: any) => {
    // 对响应数据做点什么
    return response.data
  },
  (error: any) => {
    // 对响应错误做点什么
    return Promise.reject(error)
  }
)

// 添加泛型类型支持
const request = <T = any>(config: any): Promise<T> => {
  return service(config)
}

export default request