{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"types": ["vue"], "target": "ESNext", "module": "ESNext", "isolatedModules": true, "useDefineForClassFields": true, "moduleResolution": "bundler", "allowJs": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]}