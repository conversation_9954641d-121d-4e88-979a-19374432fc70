{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.11.0", "element-plus": "^2.10.4", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "marked": "^16.2.0", "marked-highlight": "^2.2.2", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}